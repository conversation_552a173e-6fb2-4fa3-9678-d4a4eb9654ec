#!/usr/bin/env python3
"""
Test script for Chrome/ChromeDriver compatibility checking functionality.
This script can be run independently to test the compatibility checking features.
"""

import sys
import os

# Add src directory to path so we can import selenium_automation
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from selenium_automation import COAutomation
    
    def test_chrome_version():
        """Test Chrome version detection"""
        print("=== Testing Chrome Version Detection ===")
        chrome_version = COAutomation.get_chrome_version()
        if chrome_version:
            print(f"✅ Chrome version detected: {chrome_version}")
        else:
            print("❌ Could not detect Chrome version")
        return chrome_version
    
    def test_chromedriver_version():
        """Test ChromeDriver version detection"""
        print("\n=== Testing ChromeDriver Version Detection ===")
        
        # Try to find ChromeDriver in the expected location
        driver_paths = [
            os.path.join(os.path.dirname(__file__), 'src', 'Chrome Driver', 'chromedriver.exe'),
            os.path.join(os.path.dirname(__file__), 'src', 'Chrome Driver', 'chromedriver'),
            'chromedriver.exe',
            'chromedriver'
        ]
        
        chromedriver_version = None
        driver_path = None
        
        for path in driver_paths:
            if os.path.exists(path):
                driver_path = path
                chromedriver_version = COAutomation.get_chromedriver_version(path)
                if chromedriver_version:
                    print(f"✅ ChromeDriver found at: {path}")
                    print(f"✅ ChromeDriver version: {chromedriver_version}")
                    break
                else:
                    print(f"⚠️ ChromeDriver found at {path} but version could not be determined")
        
        if not chromedriver_version:
            print("❌ ChromeDriver not found or version could not be determined")
            print("Searched paths:")
            for path in driver_paths:
                print(f"  - {path} (exists: {os.path.exists(path)})")
        
        return chromedriver_version, driver_path
    
    def test_latest_chromedriver():
        """Test latest ChromeDriver version retrieval"""
        print("\n=== Testing Latest ChromeDriver Version Retrieval ===")
        try:
            latest_version = COAutomation.get_latest_chromedriver_version()
            if latest_version:
                print(f"✅ Latest ChromeDriver version: {latest_version}")
            else:
                print("❌ Could not retrieve latest ChromeDriver version")
            return latest_version
        except Exception as e:
            print(f"❌ Error retrieving latest version: {e}")
            return None
    
    def test_full_compatibility_check():
        """Test the full compatibility check"""
        print("\n=== Testing Full Compatibility Check ===")
        try:
            results = COAutomation.check_chrome_chromedriver_compatibility()
            
            print(f"Chrome Version: {results.get('chrome_version', 'Not detected')}")
            print(f"ChromeDriver Version: {results.get('chromedriver_version', 'Not found')}")
            print(f"Latest ChromeDriver: {results.get('latest_chromedriver', 'Unknown')}")
            print(f"Compatible: {results.get('compatible', False)}")
            print(f"Message: {results.get('message', 'No message')}")
            
            if 'driver_path' in results:
                print(f"Driver Path: {results['driver_path']}")
            
            return results
        except Exception as e:
            print(f"❌ Error in compatibility check: {e}")
            return None
    
    def main():
        """Main test function"""
        print("Chrome/ChromeDriver Compatibility Test")
        print("=" * 50)
        
        # Test individual components
        chrome_version = test_chrome_version()
        chromedriver_version, driver_path = test_chromedriver_version()
        latest_version = test_latest_chromedriver()
        
        # Test full compatibility check
        compatibility_results = test_full_compatibility_check()
        
        # Summary
        print("\n" + "=" * 50)
        print("SUMMARY")
        print("=" * 50)
        
        if chrome_version and chromedriver_version:
            chrome_major = chrome_version.split('.')[0]
            chromedriver_major = chromedriver_version.split('.')[0]
            
            if chrome_major == chromedriver_major:
                print("✅ Chrome and ChromeDriver major versions match")
            else:
                print(f"❌ Version mismatch: Chrome {chrome_major}.x vs ChromeDriver {chromedriver_major}.x")
        else:
            print("⚠️ Cannot determine compatibility - missing version information")
        
        if latest_version and chromedriver_version:
            if latest_version == chromedriver_version:
                print("✅ ChromeDriver is up to date")
            else:
                print(f"⚠️ ChromeDriver update available: {chromedriver_version} → {latest_version}")
        
        print("\nTest completed!")

if __name__ == "__main__":
    main()

except ImportError as e:
    print(f"Error importing selenium_automation: {e}")
    print("Make sure you're running this from the project root directory")
    print("and that all required packages are installed:")
    print("pip install -r requirements.txt")
