#!/usr/bin/env python3
"""
Simple test to verify the Chrome compatibility checking functions work.
"""

import sys
import os
import subprocess
import re

def test_basic_functionality():
    """Test basic functionality without external dependencies"""
    print("Testing basic Chrome version detection...")
    
    # Test Chrome version detection logic
    if sys.platform.startswith('win'):
        print("✅ Windows platform detected")
        
        # Test Chrome path checking
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"✅ Chrome found at: {path}")
                try:
                    # Try to get version
                    result = subprocess.run([path, "--version"], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        match = re.search(r'(\d+\.\d+\.\d+\.\d+)', result.stdout)
                        if match:
                            version = match.group(1)
                            print(f"✅ Chrome version: {version}")
                            return True
                        else:
                            print(f"⚠️ Could not parse version from: {result.stdout}")
                    else:
                        print(f"⚠️ Chrome command failed: {result.stderr}")
                except Exception as e:
                    print(f"⚠️ Error running Chrome: {e}")
            else:
                print(f"❌ Chrome not found at: {path}")
    
    elif sys.platform.startswith('darwin'):
        print("✅ macOS platform detected")
        chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        if os.path.exists(chrome_path):
            print(f"✅ Chrome found at: {chrome_path}")
        else:
            print(f"❌ Chrome not found at: {chrome_path}")
    
    elif sys.platform.startswith('linux'):
        print("✅ Linux platform detected")
        # Test common Chrome commands
        commands = ['google-chrome --version', 'google-chrome-stable --version']
        for cmd in commands:
            try:
                result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✅ Chrome command works: {cmd}")
                    match = re.search(r'(\d+\.\d+\.\d+\.\d+)', result.stdout)
                    if match:
                        version = match.group(1)
                        print(f"✅ Chrome version: {version}")
                        return True
            except Exception as e:
                print(f"⚠️ Command failed {cmd}: {e}")
    
    print("❌ Could not detect Chrome version")
    return False

def test_chromedriver_detection():
    """Test ChromeDriver detection"""
    print("\nTesting ChromeDriver detection...")
    
    # Check expected ChromeDriver location
    driver_path = os.path.join("src", "Chrome Driver", "chromedriver.exe")
    if os.path.exists(driver_path):
        print(f"✅ ChromeDriver found at: {driver_path}")
        try:
            result = subprocess.run([driver_path, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                match = re.search(r'(\d+\.\d+\.\d+\.\d+)', result.stdout)
                if match:
                    version = match.group(1)
                    print(f"✅ ChromeDriver version: {version}")
                    return True
                else:
                    print(f"⚠️ Could not parse ChromeDriver version from: {result.stdout}")
            else:
                print(f"⚠️ ChromeDriver command failed: {result.stderr}")
        except Exception as e:
            print(f"⚠️ Error running ChromeDriver: {e}")
    else:
        print(f"❌ ChromeDriver not found at: {driver_path}")
        print("This is expected if ChromeDriver hasn't been downloaded yet.")
    
    return False

def test_network_connectivity():
    """Test network connectivity for version checking"""
    print("\nTesting network connectivity...")
    
    try:
        import urllib.request
        
        # Test connection to ChromeDriver API
        urls = [
            "https://chromedriver.storage.googleapis.com/LATEST_RELEASE",
            "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions.json"
        ]
        
        for url in urls:
            try:
                with urllib.request.urlopen(url, timeout=10) as response:
                    if response.status == 200:
                        data = response.read().decode('utf-8')
                        print(f"✅ Successfully connected to: {url}")
                        if 'LATEST_RELEASE' in url:
                            print(f"   Latest ChromeDriver version: {data.strip()}")
                        return True
                    else:
                        print(f"⚠️ HTTP {response.status} from: {url}")
            except Exception as e:
                print(f"⚠️ Failed to connect to {url}: {e}")
        
        print("❌ Could not connect to ChromeDriver version APIs")
        return False
        
    except ImportError:
        print("⚠️ urllib.request not available")
        return False

def main():
    """Main test function"""
    print("Chrome/ChromeDriver Compatibility - Simple Test")
    print("=" * 50)
    
    # Test basic functionality
    chrome_detected = test_basic_functionality()
    chromedriver_detected = test_chromedriver_detection()
    network_ok = test_network_connectivity()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    if chrome_detected:
        print("✅ Chrome browser detection: WORKING")
    else:
        print("❌ Chrome browser detection: FAILED")
    
    if chromedriver_detected:
        print("✅ ChromeDriver detection: WORKING")
    else:
        print("⚠️ ChromeDriver detection: NOT FOUND (may need download)")
    
    if network_ok:
        print("✅ Network connectivity: WORKING")
    else:
        print("❌ Network connectivity: FAILED")
    
    print("\nThe Chrome compatibility checking feature should work if:")
    print("- Chrome browser detection is working")
    print("- Network connectivity is available (for updates)")
    print("- Required Python packages are installed (requests, packaging)")

if __name__ == "__main__":
    main()
