(['D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Main.py'],
 ['D:\\Setup\\SoftwareProject\\Upload CO Project\\src'],
 ['selenium', 'pandas', 'PySide6', 'webdriver_manager', 'configparser'],
 [('D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Setup\\Python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Setup\\Python\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('CO form example.csv',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\CO form example.csv',
   'DATA'),
  ('config\\settings.ini',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\config\\settings.ini',
   'DATA'),
  ('goods_sample.csv',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\goods_sample.csv',
   'DATA'),
  ('src\\Backup\\Main.py',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Backup\\Main.py',
   'DATA'),
  ('src\\Chrome Driver\\LICENSE.chromedriver',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Chrome '
   'Driver\\LICENSE.chromedriver',
   'DATA'),
  ('src\\Chrome Driver\\THIRD_PARTY_NOTICES.chromedriver',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Chrome '
   'Driver\\THIRD_PARTY_NOTICES.chromedriver',
   'DATA'),
  ('src\\Chrome Driver\\chromedriver.exe',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Chrome '
   'Driver\\chromedriver.exe',
   'DATA'),
  ('src\\Main.py',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Main.py',
   'DATA'),
  ('src\\__pycache__\\selenium_automation.cpython-311.pyc',
   'D:\\Setup\\SoftwareProject\\Upload CO '
   'Project\\src\\__pycache__\\selenium_automation.cpython-311.pyc',
   'DATA'),
  ('src\\__pycache__\\selenium_automation.cpython-313.pyc',
   'D:\\Setup\\SoftwareProject\\Upload CO '
   'Project\\src\\__pycache__\\selenium_automation.cpython-313.pyc',
   'DATA'),
  ('src\\selenium_automation.py',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\selenium_automation.py',
   'DATA'),
  ('src\\settings.ini',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\settings.ini',
   'DATA')],
 '3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_pyside6',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('Main',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Main.py',
   'PYSOURCE')],
 [('pkgutil', 'D:\\Setup\\Python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Setup\\Python\\Lib\\zipimport.py', 'PYMODULE'),
  ('struct', 'D:\\Setup\\Python\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Setup\\Python\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\Setup\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Setup\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\Setup\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\Setup\\Python\\Lib\\random.py', 'PYMODULE'),
  ('argparse', 'D:\\Setup\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Setup\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Setup\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Setup\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('statistics', 'D:\\Setup\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Setup\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Setup\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Setup\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\Setup\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Setup\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Setup\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\Setup\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\Setup\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Setup\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Setup\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Setup\\Python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('threading', 'D:\\Setup\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Setup\\Python\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('string', 'D:\\Setup\\Python\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\Setup\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'D:\\Setup\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Setup\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Setup\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\Setup\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\Setup\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Setup\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Setup\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('zipfile', 'D:\\Setup\\Python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\Setup\\Python\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Setup\\Python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Setup\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Setup\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\Setup\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\Setup\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\Setup\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Setup\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Setup\\Python\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Setup\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Setup\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\Setup\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'D:\\Setup\\Python\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Setup\\Python\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Setup\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Setup\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Setup\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\Setup\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\Setup\\Python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime',
   'D:\\Setup\\Python\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Setup\\Python\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\Setup\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\Setup\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\Setup\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'D:\\Setup\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Setup\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\Setup\\Python\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\Setup\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Setup\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Setup\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('quopri', 'D:\\Setup\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\Setup\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Setup\\Python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Setup\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Setup\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email', 'D:\\Setup\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\Setup\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\Setup\\Python\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('json', 'D:\\Setup\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Setup\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Setup\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Setup\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('tokenize', 'D:\\Setup\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Setup\\Python\\Lib\\token.py', 'PYMODULE'),
  ('pathlib', 'D:\\Setup\\Python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\Setup\\Python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'D:\\Setup\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\Setup\\Python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('contextlib', 'D:\\Setup\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('__future__', 'D:\\Setup\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('inspect', 'D:\\Setup\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Setup\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Setup\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata',
   'D:\\Setup\\Python\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast', 'D:\\Setup\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Setup\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Setup\\Python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib', 'D:\\Setup\\Python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('subprocess', 'D:\\Setup\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\Setup\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\Setup\\Python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\Setup\\Python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Setup\\Python\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Setup\\Python\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Setup\\Python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Setup\\Python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Setup\\Python\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request', 'D:\\Setup\\Python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'D:\\Setup\\Python\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Setup\\Python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\Setup\\Python\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Setup\\Python\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Setup\\Python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\Setup\\Python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\Setup\\Python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Setup\\Python\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Setup\\Python\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'D:\\Setup\\Python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'D:\\Setup\\Python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Setup\\Python\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Setup\\Python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Setup\\Python\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\Setup\\Python\\Lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'D:\\Setup\\Python\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\Setup\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\Setup\\Python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\Setup\\Python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Setup\\Python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Setup\\Python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Setup\\Python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Setup\\Python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'D:\\Setup\\Python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Setup\\Python\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Setup\\Python\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Setup\\Python\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Setup\\Python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Setup\\Python\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'D:\\Setup\\Python\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'D:\\Setup\\Python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('unittest.mock', 'D:\\Setup\\Python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'D:\\Setup\\Python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\Setup\\Python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Setup\\Python\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'D:\\Setup\\Python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'D:\\Setup\\Python\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Setup\\Python\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite', 'D:\\Setup\\Python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'D:\\Setup\\Python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\Setup\\Python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'D:\\Setup\\Python\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\Setup\\Python\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util', 'D:\\Setup\\Python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('asyncio', 'D:\\Setup\\Python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Setup\\Python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\Setup\\Python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Setup\\Python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Setup\\Python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Setup\\Python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Setup\\Python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Setup\\Python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Setup\\Python\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Setup\\Python\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Setup\\Python\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Setup\\Python\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Setup\\Python\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Setup\\Python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\Setup\\Python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Setup\\Python\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Setup\\Python\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\Setup\\Python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\Setup\\Python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Setup\\Python\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'D:\\Setup\\Python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\Setup\\Python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Setup\\Python\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Setup\\Python\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Setup\\Python\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Setup\\Python\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Setup\\Python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Setup\\Python\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\Setup\\Python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Setup\\Python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Setup\\Python\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Setup\\Python\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\Setup\\Python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Setup\\Python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Setup\\Python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Setup\\Python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\Setup\\Python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'D:\\Setup\\Python\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Setup\\Python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site', 'D:\\Setup\\Python\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\Setup\\Python\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\Setup\\Python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\Setup\\Python\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'D:\\Setup\\Python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\Setup\\Python\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\Setup\\Python\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\Setup\\Python\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\Setup\\Python\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.pager', 'D:\\Setup\\Python\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'D:\\Setup\\Python\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'D:\\Setup\\Python\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\Setup\\Python\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'D:\\Setup\\Python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('code', 'D:\\Setup\\Python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Setup\\Python\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'D:\\Setup\\Python\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Setup\\Python\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\Setup\\Python\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\Setup\\Python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\Setup\\Python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'D:\\Setup\\Python\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Setup\\Python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\Setup\\Python\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'D:\\Setup\\Python\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\Setup\\Python\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\Setup\\Python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\Setup\\Python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Setup\\Python\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'D:\\Setup\\Python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\Setup\\Python\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Setup\\Python\\Lib\\plistlib.py', 'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Setup\\Python\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'D:\\Setup\\Python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'D:\\Setup\\Python\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types', 'D:\\Setup\\Python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\Setup\\Python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Setup\\Python\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('PySide6',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('PySide6.support',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('shiboken6',
   'D:\\Setup\\Python\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('stringprep', 'D:\\Setup\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Setup\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Setup\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('selenium_automation',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\selenium_automation.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.types',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Setup\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'D:\\Setup\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Setup\\Python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('configparser', 'D:\\Setup\\Python\\Lib\\configparser.py', 'PYMODULE'),
  ('webdriver_manager.chrome',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('dotenv',
   'D:\\Setup\\Python\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\Setup\\Python\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\Setup\\Python\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\Setup\\Python\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'D:\\Setup\\Python\\Lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'D:\\Setup\\Python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('idna',
   'D:\\Setup\\Python\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Setup\\Python\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Setup\\Python\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Setup\\Python\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Setup\\Python\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('requests',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Setup\\Python\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'D:\\Setup\\Python\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('requests.__version__',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Setup\\Python\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Setup\\Python\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('uuid', 'D:\\Setup\\Python\\Lib\\uuid.py', 'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.html5.application_cache',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\html5\\application_cache.py',
   'PYMODULE'),
  ('selenium.webdriver.common.html5',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\html5\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Setup\\Python\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Setup\\Python\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Setup\\Python\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Setup\\Python\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Setup\\Python\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Setup\\Python\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\Setup\\Python\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'D:\\Setup\\Python\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('pandas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('six', 'D:\\Setup\\Python\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pickletools', 'D:\\Setup\\Python\\Lib\\pickletools.py', 'PYMODULE'),
  ('doctest', 'D:\\Setup\\Python\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Setup\\Python\\Lib\\pdb.py', 'PYMODULE'),
  ('bdb', 'D:\\Setup\\Python\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Setup\\Python\\Lib\\cmd.py', 'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pytz',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\Setup\\Python\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\Setup\\Python\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\Setup\\Python\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\Setup\\Python\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Setup\\Python\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Setup\\Python\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Setup\\Python\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Setup\\Python\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\Setup\\Python\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('pandas.io.xml',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'D:\\Setup\\Python\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE')],
 [('src\\Chrome Driver\\chromedriver.exe',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Chrome '
   'Driver\\chromedriver.exe',
   'BINARY'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python313.dll', 'D:\\Setup\\Python\\python313.dll', 'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\Setup\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Setup\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Setup\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Setup\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Setup\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Setup\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\Setup\\Python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Setup\\Python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Setup\\Python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Setup\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Setup\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Setup\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Setup\\Python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\Setup\\Python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Setup\\Python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Setup\\Python\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\Setup\\Python\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Setup\\Python\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\Setup\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Setup\\Python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\Setup\\Python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\Setup\\Python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Setup\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Setup\\Python\\python3.dll', 'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\Setup\\Python\\DLLs\\sqlite3.dll', 'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY')],
 [],
 [],
 [('CO form example.csv',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\CO form example.csv',
   'DATA'),
  ('config\\settings.ini',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\config\\settings.ini',
   'DATA'),
  ('goods_sample.csv',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\goods_sample.csv',
   'DATA'),
  ('src\\Backup\\Main.py',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Backup\\Main.py',
   'DATA'),
  ('src\\Chrome Driver\\LICENSE.chromedriver',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Chrome '
   'Driver\\LICENSE.chromedriver',
   'DATA'),
  ('src\\Chrome Driver\\THIRD_PARTY_NOTICES.chromedriver',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Chrome '
   'Driver\\THIRD_PARTY_NOTICES.chromedriver',
   'DATA'),
  ('src\\Main.py',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\Main.py',
   'DATA'),
  ('src\\__pycache__\\selenium_automation.cpython-311.pyc',
   'D:\\Setup\\SoftwareProject\\Upload CO '
   'Project\\src\\__pycache__\\selenium_automation.cpython-311.pyc',
   'DATA'),
  ('src\\__pycache__\\selenium_automation.cpython-313.pyc',
   'D:\\Setup\\SoftwareProject\\Upload CO '
   'Project\\src\\__pycache__\\selenium_automation.cpython-313.pyc',
   'DATA'),
  ('src\\selenium_automation.py',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\selenium_automation.py',
   'DATA'),
  ('src\\settings.ini',
   'D:\\Setup\\SoftwareProject\\Upload CO Project\\src\\settings.ini',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Setup\\Python\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Setup\\Python\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v107\\py.typed',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v107\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v108\\py.typed',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v108\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v109\\py.typed',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v109\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'D:\\Setup\\Python\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Setup\\Python\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\Setup\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\Setup\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lg.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_lg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lg.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_lg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'D:\\Setup\\Python\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('numpy-2.2.4.dist-info\\RECORD',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.4.dist-info\\INSTALLER',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\METADATA',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.4.dist-info\\LICENSE.txt',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\entry_points.txt',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\WHEEL',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.4.dist-info\\DELVEWHEEL',
   'D:\\Setup\\Python\\Lib\\site-packages\\numpy-2.2.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'D:\\Setup\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'D:\\Setup\\SoftwareProject\\Upload CO '
   'Project\\build\\co_automation\\base_library.zip',
   'DATA')])
