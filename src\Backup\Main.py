import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QTabWidget, 
                           QWidget, QVBoxLayout, QPushButton, QTableWidget,
                           QTableWidgetItem, QFileDialog, QMessageBox, QLabel,
                           QCheckBox, QGroupBox, QHBoxLayout, QDialog, QScrollArea,
                           QTextEdit, QLineEdit, QFormLayout, QSpinBox, QComboBox)
from PySide6.QtCore import Qt, QThread, Signal
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import os
import configparser
from datetime import datetime
import time
import threading

import selenium_automation

class DataPreviewDialog(QDialog):
    def __init__(self, data: pd.DataFrame, title: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setGeometry(100, 100, 1200, 600)  # Increased width to 1200
        
        layout = QVBoxLayout(self)
        
        # Create table
        table = QTableWidget()
        table.setRowCount(len(data))
        table.setColumnCount(len(data.columns))
        table.setHorizontalHeaderLabels(data.columns)
        
        # Set header height to double
        header = table.horizontalHeader()
        header.setDefaultSectionSize(header.defaultSectionSize() * 2)
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #2196F3;
                color: white;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # Enable sorting
        table.setSortingEnabled(True)
        
        # Fill data without text wrapping
        for i in range(len(data)):
            for j in range(len(data.columns)):
                item = QTableWidgetItem(str(data.iloc[i, j]))
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                table.setItem(i, j, item)
        
        # Set fixed row height
        table.verticalHeader().setDefaultSectionSize(30)
        
        # Adjust column widths
        table.resizeColumnsToContents()
        
        # Add scroll area
        scroll = QScrollArea()
        scroll.setWidget(table)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # Add close button
        close_btn = QPushButton("Close")
        close_btn.setFixedHeight(30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

class ErrorLogDialog(QDialog):
    def __init__(self, error_message: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Error Log")
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setGeometry(100, 100, 600, 200)
        
        layout = QVBoxLayout(self)
        
        # Create text area for error message
        error_text = QTextEdit()
        error_text.setReadOnly(True)
        error_text.setPlainText(error_message)
        error_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(error_text)
        
        # Add close button
        close_btn = QPushButton("Close")
        close_btn.setFixedHeight(30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

class AutomationWorker(QThread):
    finished = Signal()
    error = Signal(str)
    progress = Signal(str)
    batch_completed = Signal(str)  # New signal for batch completion

    def __init__(self, parent=None):
        super().__init__(parent)
        self.automation = None
        self.settings_data = None
        self.goods_data = None
        self.task = None
        self.driver = None
        self.is_running = False
        self._driver_lock = threading.Lock()  # Thread-safe lock for driver operations
        self._batch_size = 5  # Process batches of 5 items at a time
        self._retry_count = 3  # Number of retries for failed operations
        self._retry_delay = 2  # Delay between retries in seconds

    def set_task(self, task, settings_data=None, goods_data=None):
        self.task = task
        self.settings_data = settings_data
        self.goods_data = goods_data

    def _initialize_driver(self):
        """Initialize Chrome driver with optimized settings"""
        if self.driver is not None:
            return

        try:
            chrome_options = Options()
            chrome_options.add_argument('--start-maximized')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            
            # Get the path to the Chrome driver in src folder
            if getattr(sys, 'frozen', False):
                application_path = os.path.dirname(sys.executable)
                driver_path = os.path.join(application_path, 'src', 'Chrome Driver', 'chromedriver.exe')
            else:
                driver_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Chrome Driver', 'chromedriver.exe')
            
            if not os.path.exists(driver_path):
                raise FileNotFoundError(f"Chrome driver not found at: {driver_path}")
            
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(30)  # Set page load timeout
            self.driver.implicitly_wait(10)  # Set implicit wait time
            
        except Exception as e:
            self.error.emit(f"Failed to initialize Chrome driver: {str(e)}")
            raise

    def _process_batch(self, batch_data, task_type):
        """Process a batch of data with retry mechanism"""
        for attempt in range(self._retry_count):
            try:
                with self._driver_lock:
                    if task_type == "fill_form":
                        self.automation.fill_form(batch_data)
                    elif task_type == "fill_goods":
                        self.automation.fill_goods(batch_data)
                return True
            except Exception as e:
                if attempt < self._retry_count - 1:
                    self.progress.emit(f"Retry {attempt + 1}/{self._retry_count} for batch {batch_data['Batch No'].iloc[0]}")
                    time.sleep(self._retry_delay)
                else:
                    self.error.emit(f"Failed to process batch {batch_data['Batch No'].iloc[0]} after {self._retry_count} attempts: {str(e)}")
                    return False

    def run(self):
        try:
            self.is_running = True
            
            # Initialize Chrome driver if needed
            if self.driver is None:
                self._initialize_driver()
            
            if self.task == "start_co_creation":
                with self._driver_lock:
                    self.automation.start_co_creation()
                self.finished.emit()
            
            elif self.task == "fill_form":
                if self.settings_data is None:
                    raise ValueError("Settings data is required for form filling")
                
                # Process data in batches
                batch_numbers = self.settings_data['Batch No'].unique()
                for i in range(0, len(batch_numbers), self._batch_size):
                    if not self.is_running:
                        break
                    
                    current_batches = batch_numbers[i:i + self._batch_size]
                    for batch_no in current_batches:
                        if not self.is_running:
                            break
                        
                        try:
                            batch_settings = self.settings_data[self.settings_data['Batch No'] == batch_no]
                            if self._process_batch(batch_settings, "fill_form"):
                                self.batch_completed.emit(f"Completed form filling for batch {batch_no}")
                        except Exception as e:
                            self.error.emit(f"Error processing batch {batch_no}: {str(e)}")
                            continue
                
                self.finished.emit()
            
            elif self.task == "fill_goods":
                if self.goods_data is None:
                    raise ValueError("Goods data is required for goods filling")
                
                # Process data in batches
                batch_numbers = self.goods_data['Batch No'].unique()
                for i in range(0, len(batch_numbers), self._batch_size):
                    if not self.is_running:
                        break
                    
                    current_batches = batch_numbers[i:i + self._batch_size]
                    for batch_no in current_batches:
                        if not self.is_running:
                            break
                        
                        try:
                            batch_goods = self.goods_data[self.goods_data['Batch No'] == batch_no]
                            if self._process_batch(batch_goods, "fill_goods"):
                                self.batch_completed.emit(f"Completed goods filling for batch {batch_no}")
                                
                                # After completing the batch, click the Khai báo C/O link
                                try:
                                    with self._driver_lock:
                                        khai_bao_link = WebDriverWait(self.driver, 15).until(
                                            EC.element_to_be_clickable((By.CSS_SELECTOR, "a.rtIn[href='/CertificatesUpgrade/Business/CertificateEdit.aspx']"))
                                        )
                                        khai_bao_link.click()
                                        time.sleep(3)  # Wait for the new page to load
                                        self.progress.emit(f"Opened new CO form for next batch")
                                except Exception as e:
                                    self.error.emit(f"Error opening new CO form: {str(e)}")
                                    continue
                        except Exception as e:
                            self.error.emit(f"Error processing batch {batch_no}: {str(e)}")
                            continue
                
                self.finished.emit()
            
        except Exception as e:
            self.error.emit(str(e))
        finally:
            self.is_running = False
            # Only close the browser if it's the last task
            if self.task == "fill_goods" and self.driver:
                with self._driver_lock:
                    self.driver.quit()

    def stop(self):
        """Stop the automation process"""
        self.is_running = False

class SettingsTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.settings_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        'config', 'settings.ini')
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Reduced margins
        
        # Create form layout for settings
        form_layout = QFormLayout()
        form_layout.setSpacing(2)  # Reduced spacing
        
        # URL Settings
        url_group = QGroupBox("URL Settings")
        url_layout = QFormLayout()
        url_layout.setSpacing(2)
        self.base_url = QLineEdit()
        url_layout.addRow("Base URL:", self.base_url)
        url_group.setLayout(url_layout)
        
        # Credentials
        cred_group = QGroupBox("Credentials")
        cred_layout = QFormLayout()
        cred_layout.setSpacing(2)
        self.username = QLineEdit()
        self.password = QLineEdit()
        self.password.setEchoMode(QLineEdit.Password)
        cred_layout.addRow("Username:", self.username)
        cred_layout.addRow("Password:", self.password)
        cred_group.setLayout(cred_layout)
        
        # Path Settings
        path_group = QGroupBox("Path Settings")
        path_layout = QFormLayout()
        path_layout.setSpacing(2)
        self.chrome_driver = QLineEdit()
        self.download_dir = QLineEdit()
        path_layout.addRow("Chrome Driver:", self.chrome_driver)
        path_layout.addRow("Download Directory:", self.download_dir)
        path_group.setLayout(path_layout)
        
        # Browser Settings
        browser_group = QGroupBox("Browser Settings")
        browser_layout = QFormLayout()
        browser_layout.setSpacing(2)
        self.headless = QCheckBox()
        self.timeout = QSpinBox()
        self.timeout.setRange(10, 120)
        self.timeout.setValue(30)
        browser_layout.addRow("Headless Mode:", self.headless)
        browser_layout.addRow("Timeout (seconds):", self.timeout)
        browser_group.setLayout(browser_layout)
        
        # Add all groups to main layout
        layout.addWidget(url_group)
        layout.addWidget(cred_group)
        layout.addWidget(path_group)
        layout.addWidget(browser_group)
        
        # Add save button
        save_btn = QPushButton("Save Settings")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                min-width: 100px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        # Add stretch to push everything up
        layout.addStretch()

    def load_settings(self):
        try:
            config = configparser.ConfigParser()
            config.read(self.settings_path)
            
            # Load URL settings
            self.base_url.setText(config.get('URL', 'base_url', fallback=''))
            
            # Load credentials
            self.username.setText(config.get('Credentials', 'username', fallback=''))
            self.password.setText(config.get('Credentials', 'password', fallback=''))
            
            # Load path settings
            self.chrome_driver.setText(config.get('Paths', 'chrome_driver', fallback='auto'))
            self.download_dir.setText(config.get('Paths', 'download_dir', fallback='downloads'))
            
            # Load browser settings
            self.headless.setChecked(config.getboolean('Settings', 'headless', fallback=False))
            self.timeout.setValue(config.getint('Settings', 'timeout', fallback=30))
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load settings: {str(e)}")

    def save_settings(self):
        try:
            if getattr(sys, 'frozen', False):
                # When running as executable, save to the executable's directory
                save_path = os.path.join(os.path.dirname(sys.executable), 'config', 'settings.ini')
            else:
                save_path = self.settings_path

            # Ensure config directory exists
            config_dir = os.path.dirname(save_path)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            config = configparser.ConfigParser()
            
            # Save URL settings
            config['URL'] = {
                'base_url': self.base_url.text()
            }
            
            # Save credentials
            config['Credentials'] = {
                'username': self.username.text(),
                'password': self.password.text()
            }
            
            # Path settings
            config['Paths'] = {
                'chrome_driver': self.chrome_driver.text(),
                'download_dir': self.download_dir.text()
            }
            
            # Browser settings
            config['Settings'] = {
                'headless': str(self.headless.isChecked()).lower(),
                'timeout': str(self.timeout.value())
            }
            
            # Save to file
            with open(save_path, 'w') as f:
                config.write(f)
            
            # Reload settings in main window
            if self.parent:
                self.parent.load_settings()
            
            QMessageBox.information(self, "Success", "Settings saved successfully!")
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to save settings: {str(e)}")

    def validate_settings_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate settings CSV data with optimized performance"""
        # Cache validation results
        cache_key = hash(str(df))
        if hasattr(self, '_validation_cache') and cache_key in self._validation_cache:
            return self._validation_cache[cache_key]
        
        # Check for required columns using set operations for better performance
        missing_columns = set(self.required_settings_columns) - set(df.columns)
        if missing_columns:
            result = (False, f"Missing required columns: {', '.join(missing_columns)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Use vectorized operations for empty value checks
        empty_cols = [col for col in self.required_settings_columns if df[col].isna().any()]
        if empty_cols:
            result = (False, f"Columns with empty values: {', '.join(empty_cols)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize duplicate check
        if df['Batch No'].duplicated().any():
            result = (False, "Batch No must be unique in settings CSV")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize date validation using vectorized operations
        try:
            date_pattern = r'^(\d{2})/(\d{2})/(\d{4})$'
            date_mask = df['Date'].str.match(date_pattern)
            if not date_mask.all():
                result = (False, "Date must be in DD/MM/YYYY format")
                self._validation_cache[cache_key] = result
                return result
            
            # Extract date components efficiently
            dates = df['Date'].str.extract(date_pattern)
            days = pd.to_numeric(dates[0])
            months = pd.to_numeric(dates[1])
            years = pd.to_numeric(dates[2])
            
            # Validate date components
            if not ((days.between(1, 31)) & (months.between(1, 12)) & (years.str.len() == 4)).all():
                result = (False, "Invalid date values in DD/MM/YYYY format")
                self._validation_cache[cache_key] = result
                return result
        except Exception as e:
            result = (False, f"Invalid date format in 'Date' column. Use DD/MM/YYYY format. Error: {str(e)}")
            self._validation_cache[cache_key] = result
            return result
        
        result = (True, "Settings data is valid")
        self._validation_cache[cache_key] = result
        return result
    
    def validate_goods_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate goods CSV data with optimized performance"""
        # Cache validation results
        cache_key = hash(str(df))
        if hasattr(self, '_validation_cache') and cache_key in self._validation_cache:
            return self._validation_cache[cache_key]
        
        # Check for required columns using set operations
        missing_columns = set(self.required_goods_columns) - set(df.columns)
        if missing_columns:
            result = (False, f"Missing required columns: {', '.join(missing_columns)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Use vectorized operations for empty value checks
        empty_cols = [col for col in self.required_goods_columns if df[col].isna().any()]
        if empty_cols:
            result = (False, f"Columns with empty values: {', '.join(empty_cols)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize numeric validation using vectorized operations
        try:
            numeric_cols = ['Quantity', 'Gross weight', 'FOB value']
            df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
            
            # Check for positive numbers
            invalid_nums = df[numeric_cols].le(0).any()
            if invalid_nums.any():
                invalid_cols = invalid_nums[invalid_nums].index.tolist()
                result = (False, f"Values must be greater than 0 in columns: {', '.join(invalid_cols)}")
                self._validation_cache[cache_key] = result
                return result
        except Exception as e:
            result = (False, f"Invalid number format in numeric columns: {str(e)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize date validation using vectorized operations
        try:
            date_pattern = r'^(\d{2})/(\d{2})/(\d{4})$'
            date_mask = df['Invoice Date'].str.match(date_pattern)
            if not date_mask.all():
                result = (False, "Invoice Date must be in DD/MM/YYYY format")
                self._validation_cache[cache_key] = result
                return result
            
            # Extract date components efficiently
            dates = df['Invoice Date'].str.extract(date_pattern)
            days = pd.to_numeric(dates[0])
            months = pd.to_numeric(dates[1])
            years = pd.to_numeric(dates[2])
            
            # Validate date components
            if not ((days.between(1, 31)) & (months.between(1, 12)) & (years.str.len() == 4)).all():
                result = (False, "Invalid date values in Invoice Date")
                self._validation_cache[cache_key] = result
                return result
        except Exception as e:
            result = (False, f"Invalid date format in 'Invoice Date' column: {str(e)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize Origin Criteria validation
        valid_criteria = {'CC', 'CTH', 'CTSH', 'CTC', 'PSR', 'WO'}
        invalid_criteria = set(df['Origin Criteria']) - valid_criteria
        if invalid_criteria:
            result = (False, f"Invalid Origin Criteria values: {', '.join(invalid_criteria)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize PackageQuantity validation if present
        if 'PackageQuantity' in df.columns and not df['PackageQuantity'].isna().all():
            try:
                df['PackageQuantity'] = pd.to_numeric(df['PackageQuantity'].dropna())
                if (df['PackageQuantity'] <= 0).any():
                    result = (False, "PackageQuantity must be greater than 0")
                    self._validation_cache[cache_key] = result
                    return result
            except Exception as e:
                result = (False, f"Invalid number format in 'PackageQuantity' column: {str(e)}")
                self._validation_cache[cache_key] = result
                return result
        
        result = (True, "Goods data is valid")
        self._validation_cache[cache_key] = result
        return result
    
    def show_error_log(self, error_message: str):
        """Show error log dialog"""
        self.error_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_message}")
        dialog = ErrorLogDialog("\n".join(self.error_log), self)
        dialog.exec_()

    def import_settings(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Settings CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                # Use pandas optimized reading with caching
                cache_key = hash(file_name)
                if hasattr(self, '_settings_cache') and cache_key in self._settings_cache:
                    self.settings_data = self._settings_cache[cache_key]
                else:
                    # Optimize CSV reading with specific dtypes and parsing dates
                    self.settings_data = pd.read_csv(
                        file_name,
                        dtype={
                            'Batch No': str,
                            'Form Value': str,
                            'Importing Country': str,
                            'Export Declaration Number': str,
                            'Transportation type': str,
                            'Port of Loading': str,
                            'Port of Discharge': str,
                            'Consignee\'s name': str,
                            'Address line 1': str,
                            'Vessel\'s Name/Aircraft etc': str
                        },
                        parse_dates=['Date', 'Departure date'],
                        date_parser=lambda x: pd.to_datetime(x, format='%d/%m/%Y')
                    )
                    self._settings_cache[cache_key] = self.settings_data
                
                # Validate the data
                is_valid, message = self.validate_settings_csv(self.settings_data)
                if not is_valid:
                    self.show_error_log(f"Settings Validation Error: {message}")
                    self.settings_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.settings_data, "Settings Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing settings: {str(e)}")
                self.settings_data = None

    def import_goods(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Goods CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                # Use pandas optimized reading with caching
                cache_key = hash(file_name)
                if hasattr(self, '_goods_cache') and cache_key in self._goods_cache:
                    self.goods_data = self._goods_cache[cache_key]
                else:
                    # Optimize CSV reading with specific dtypes and parsing dates
                    self.goods_data = pd.read_csv(
                        file_name,
                        dtype={
                            'Batch No': str,
                            'Exporting HS code': str,
                            'Goods description': str,
                            'Quantity': float,
                            'Quantity Unit': str,
                            'Gross weight': float,
                            'Gross weight unit': str,
                            'Invoice number': str,
                            'Mark and number on package': str,
                            'FOB value': float,
                            'Currency': str,
                            'Origin Criteria': str,
                            'PackageQuantity': float,
                            'PackageQuantityUnit': str
                        },
                        parse_dates=['Invoice Date'],
                        date_parser=lambda x: pd.to_datetime(x, format='%d/%m/%Y')
                    )
                    
                    # Ensure invoice numbers are strings with leading zeros
                    max_invoice_length = len(self.goods_data['Invoice number'].astype(str).max())
                    self.goods_data['Invoice number'] = self.goods_data['Invoice number'].astype(str).str.zfill(max_invoice_length)
                    
                    self._goods_cache[cache_key] = self.goods_data
                
                # Validate the data
                is_valid, message = self.validate_goods_csv(self.goods_data)
                if not is_valid:
                    self.show_error_log(f"Goods Validation Error: {message}")
                    self.goods_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.goods_data, "Goods Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing goods: {str(e)}")
                self.goods_data = None
    
    def start_background_task(self, task, settings_data=None, goods_data=None):
        """Start a background task with the automation worker"""
        if self.worker is None or not self.worker.isRunning():
            if self.worker is None:
                self.worker = AutomationWorker(self)
                self.worker.finished.connect(self.on_worker_finished)
                self.worker.error.connect(self.on_worker_error)
                self.worker.progress.connect(self.on_worker_progress)
            
            self.worker.set_task(task, settings_data, goods_data)
            self.worker.start()
        else:
            self.show_error_log("Previous automation task is still running")

    def on_worker_progress(self, message):
        """Handle progress updates from the worker"""
        print(f"Progress: {message}")

    def on_worker_finished(self):
        """Handle completion of the worker task"""
        print("Automation task completed successfully")

    def on_worker_error(self, error_message):
        """Handle errors from the worker"""
        self.show_error_log(error_message)

    def start_co(self):
        self.start_background_task("start_co_creation")
    
    def fill_co_form(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)
    
    def fill_goods(self):
        self.start_background_task("fill_goods", goods_data=self.goods_data)
    
    def fill_all(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)

    def show_settings_data(self):
        """Show current settings data in a popup"""
        if self.settings_data is not None:
            preview = DataPreviewDialog(self.settings_data, "Current Settings Data", self)
            preview.exec_()
        else:
            self.show_error_log("No C/O Form Information available. Please import data first.")
    
    def show_goods_data(self):
        """Show current goods data in a popup"""
        if self.goods_data is not None:
            preview = DataPreviewDialog(self.goods_data, "Current Goods Data", self)
            preview.exec_()
        else:
            self.show_error_log("No Goods Data available. Please import data first.")

    def closeEvent(self, event):
        """Handle application close event"""
        try:
            # Stop the automation worker if it's running
            if self.worker and self.worker.isRunning():
                self.worker.stop()
                self.worker.wait()
            
            # Close the Chrome driver
            if self.worker and self.worker.driver:
                self.worker.driver.quit()
        except Exception as e:
            print(f"Error closing Chrome driver: {str(e)}")
        event.accept()

    def fill_co_form_manual(self):
        """Fill CO form for a selected batch"""
        if self.settings_data is None:
            self.show_error_log("No C/O Form Information available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.settings_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_settings = self.settings_data[self.settings_data['Batch No'] == selected_batch]
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch] if self.goods_data is not None else None
            
            # Start the automation task
            self.start_background_task("fill_form", batch_settings, batch_goods)
    
    def fill_goods_manual(self):
        """Fill goods for a selected batch"""
        if self.goods_data is None:
            self.show_error_log("No Goods Data available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.goods_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch]
            
            # Start the automation task
            self.start_background_task("fill_goods", goods_data=batch_goods)

class BatchSelectionDialog(QDialog):
    def __init__(self, batch_numbers, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Batch Number")
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setGeometry(100, 100, 300, 150)
        
        layout = QVBoxLayout(self)
        
        # Create combo box for batch selection
        self.batch_combo = QComboBox()
        self.batch_combo.addItems(batch_numbers)
        self.batch_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(img/down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(QLabel("Select Batch Number:"))
        layout.addWidget(self.batch_combo)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        ok_btn = QPushButton("OK")
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        ok_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def get_selected_batch(self):
        return self.batch_combo.currentText()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # Initialize caches
        self._settings_cache = {}
        self._goods_cache = {}
        self._validation_cache = {}
        
        # Update settings path handling
        if getattr(sys, 'frozen', False):
            self.settings_path = os.path.join(sys._MEIPASS, 'config', 'settings.ini')
        else:
            self.settings_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        'config', 'settings.ini')
        
        self.setWindowTitle("CO Automation Tool")
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        
        # Get screen geometry and set window size
        screen = QApplication.primaryScreen().geometry()
        window_width = 270
        window_height = 270
        x = screen.width() - window_width - 100
        y = 100
        self.setGeometry(x, y, window_width, window_height)
        
        # Create central widget and main layout with optimized spacing
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(2)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Create tab widget with optimized settings
        self.tab_widget = QTabWidget()
        self.tab_widget.setDocumentMode(True)  # Use native tab style
        self.tab_widget.setMovable(True)  # Allow tab reordering
        
        # Create main tabs with optimized layouts
        data_import_tab = QWidget()
        data_import_layout = QVBoxLayout(data_import_tab)
        data_import_layout.setSpacing(2)
        data_import_layout.setContentsMargins(5, 5, 5, 5)
        
        # Common button style with optimized appearance
        button_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 10px;
                border-radius: 3px;
                min-width: 180px;
                text-align: left;
                padding-left: 10px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
        """
        
        # Create buttons with optimized settings
        self.import_settings_btn = QPushButton("Import C/O Form Information")
        self.import_settings_btn.setStyleSheet(button_style)
        self.import_settings_btn.clicked.connect(self.import_settings)
        
        self.import_goods_btn = QPushButton("Import Goods Data")
        self.import_goods_btn.setStyleSheet(button_style)
        self.import_goods_btn.clicked.connect(self.import_goods)
        
        # Add buttons to layout with optimized spacing
        data_import_layout.addWidget(self.import_settings_btn)
        data_import_layout.addWidget(self.import_goods_btn)
        data_import_layout.addStretch()
        
        # Create CO tab with optimized layout
        co_tab = QWidget()
        co_layout = QVBoxLayout(co_tab)
        co_layout.setSpacing(2)
        co_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create CO buttons with optimized settings
        self.start_co_btn = QPushButton("Start CO Creation")
        self.start_co_btn.setStyleSheet(button_style)
        self.start_co_btn.clicked.connect(self.start_co)
        
        self.fill_co_form_btn = QPushButton("Fill CO Form")
        self.fill_co_form_btn.setStyleSheet(button_style)
        self.fill_co_form_btn.clicked.connect(self.fill_co_form)
        
        self.fill_goods_btn = QPushButton("Fill Goods")
        self.fill_goods_btn.setStyleSheet(button_style)
        self.fill_goods_btn.clicked.connect(self.fill_goods)
        
        self.fill_all_btn = QPushButton("Fill All")
        self.fill_all_btn.setStyleSheet(button_style)
        self.fill_all_btn.clicked.connect(self.fill_all)
        
        # Add CO buttons to layout with optimized spacing
        co_layout.addWidget(self.start_co_btn)
        co_layout.addWidget(self.fill_co_form_btn)
        co_layout.addWidget(self.fill_goods_btn)
        co_layout.addWidget(self.fill_all_btn)
        co_layout.addStretch()
        
        # Create config tab with optimized layout
        config_tab = QWidget()
        config_layout = QVBoxLayout(config_tab)
        config_layout.setSpacing(2)
        config_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create settings group with optimized layout
        settings_group = QGroupBox("Settings")
        settings_layout = QFormLayout(settings_group)
        settings_layout.setSpacing(2)
        
        # Create settings fields with optimized appearance
        self.base_url = QLineEdit()
        self.username = QLineEdit()
        self.password = QLineEdit()
        self.password.setEchoMode(QLineEdit.Password)
        self.chrome_driver = QLineEdit()
        self.download_dir = QLineEdit()
        self.headless = QCheckBox("Headless Mode")
        self.timeout = QSpinBox()
        self.timeout.setRange(10, 60)
        self.timeout.setValue(30)
        
        # Add settings fields to layout
        settings_layout.addRow("Base URL:", self.base_url)
        settings_layout.addRow("Username:", self.username)
        settings_layout.addRow("Password:", self.password)
        settings_layout.addRow("Chrome Driver:", self.chrome_driver)
        settings_layout.addRow("Download Directory:", self.download_dir)
        settings_layout.addRow("", self.headless)
        settings_layout.addRow("Timeout (seconds):", self.timeout)
        
        # Create save button with optimized appearance
        self.save_settings_btn = QPushButton("Save Settings")
        self.save_settings_btn.setStyleSheet(button_style)
        self.save_settings_btn.clicked.connect(self.save_settings)
        
        # Add settings components to config layout
        config_layout.addWidget(settings_group)
        config_layout.addWidget(self.save_settings_btn)
        config_layout.addStretch()
        
        # Add tabs to tab widget
        self.tab_widget.addTab(data_import_tab, "Data Import")
        self.tab_widget.addTab(co_tab, "Certificate of Origin")
        self.tab_widget.addTab(config_tab, "Configuration")
        
        # Add tab widget to main layout
        layout.addWidget(self.tab_widget)
        
        # Initialize variables
        self.settings_data = None
        self.goods_data = None
        self.worker = None
        self.chrome_driver_path = None
        self.error_log = []
        
        # Define required columns for validation
        self.required_settings_columns = [
            'Batch No','Form Value', 'Importing Country', 'Export Declaration Number', 'Date',
            'Transportation type', 'Port of Loading', 'Port of Discharge',
            'Consignee\'s name', 'Address line 1', 'Vessel\'s Name/Aircraft etc',
            'Departure date'
        ]
        
        self.required_goods_columns = [
            'Batch No','Exporting HS code', 'Goods description', 'Quantity', 'Quantity Unit',
            'Gross weight', 'Gross weight unit', 'Invoice number', 'Invoice Date',
            'Mark and number on package', 'FOB value', 'Currency', 'Origin Criteria'
        ]
        
        # Define optional columns that should be validated if present
        self.optional_goods_columns = [
            'PackageQuantity', 'PackageQuantityUnit'
        ]
        
        # Load settings
        self.load_settings()
        
        # Connect worker signals
        if self.worker:
            self.worker.batch_completed.connect(self.on_batch_completed)

    def load_settings(self):
        """Load settings from settings.ini file"""
        try:
            # First try to load from bundled settings
            if getattr(sys, 'frozen', False):
                bundled_settings = os.path.join(sys._MEIPASS, 'config', 'settings.ini')
                if os.path.exists(bundled_settings):
                    self.config = configparser.ConfigParser()
                    self.config.read(bundled_settings)
                else:
                    raise FileNotFoundError("Bundled settings.ini not found")
            else:
                # In development mode, use the original path
                if not os.path.exists(self.settings_path):
                    # Create default settings if file doesn't exist
                    self.create_default_settings()
                self.config = configparser.ConfigParser()
                self.config.read(self.settings_path)
            
            # Ensure all required sections exist
            required_sections = ['URL', 'Credentials', 'Paths', 'Settings']
            for section in required_sections:
                if section not in self.config:
                    self.config[section] = {}
            
            # Set default values if not present
            if 'base_url' not in self.config['URL']:
                self.config['URL']['base_url'] = 'https://ecosys.gov.vn/CertificatesUpgrade/Business/CertificateEdit.aspx'
            
            if 'username' not in self.config['Credentials']:
                self.config['Credentials']['username'] = ''
            if 'password' not in self.config['Credentials']:
                self.config['Credentials']['password'] = ''
            
            if 'chrome_driver' not in self.config['Paths']:
                self.config['Paths']['chrome_driver'] = 'auto'
            if 'download_dir' not in self.config['Paths']:
                self.config['Paths']['download_dir'] = 'downloads'
            
            if 'headless' not in self.config['Settings']:
                self.config['Settings']['headless'] = 'false'
            if 'timeout' not in self.config['Settings']:
                self.config['Settings']['timeout'] = '30'
            
            # Save any changes to the writable location if running as executable
            if getattr(sys, 'frozen', False):
                save_path = os.path.join(os.path.dirname(sys.executable), 'config', 'settings.ini')
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                with open(save_path, 'w') as configfile:
                    self.config.write(configfile)
                
        except Exception as e:
            msg = QMessageBox(self)
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.critical(self, "Error", f"Failed to load settings: {str(e)}")
            sys.exit(1)

    def create_default_settings(self):
        """Create default settings.ini file"""
        try:
            # Ensure config directory exists
            config_dir = os.path.dirname(self.settings_path)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            config = configparser.ConfigParser()
            
            config['URL'] = {
                'base_url': 'https://ecosys.gov.vn/CertificatesUpgrade/Business/CertificateEdit.aspx'
            }
            
            config['Credentials'] = {
                'username': '',
                'password': ''
            }
            
            config['Paths'] = {
                'chrome_driver': 'auto',
                'download_dir': 'downloads'
            }
            
            config['Settings'] = {
                'headless': 'false',
                'timeout': '30'
            }
            
            with open(self.settings_path, 'w') as configfile:
                config.write(configfile)
                
        except Exception as e:
            msg = QMessageBox(self)
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.critical(self, "Error", f"Failed to create default settings: {str(e)}")
            sys.exit(1)
    
    def validate_settings_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate settings CSV data with optimized performance"""
        # Cache validation results
        cache_key = hash(str(df))
        if hasattr(self, '_validation_cache') and cache_key in self._validation_cache:
            return self._validation_cache[cache_key]
        
        # Check for required columns using set operations for better performance
        missing_columns = set(self.required_settings_columns) - set(df.columns)
        if missing_columns:
            result = (False, f"Missing required columns: {', '.join(missing_columns)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Use vectorized operations for empty value checks
        empty_cols = [col for col in self.required_settings_columns if df[col].isna().any()]
        if empty_cols:
            result = (False, f"Columns with empty values: {', '.join(empty_cols)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize duplicate check
        if df['Batch No'].duplicated().any():
            result = (False, "Batch No must be unique in settings CSV")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize date validation using vectorized operations
        try:
            date_pattern = r'^(\d{2})/(\d{2})/(\d{4})$'
            date_mask = df['Date'].str.match(date_pattern)
            if not date_mask.all():
                result = (False, "Date must be in DD/MM/YYYY format")
                self._validation_cache[cache_key] = result
                return result
            
            # Extract date components efficiently
            dates = df['Date'].str.extract(date_pattern)
            days = pd.to_numeric(dates[0])
            months = pd.to_numeric(dates[1])
            years = pd.to_numeric(dates[2])
            
            # Validate date components
            if not ((days.between(1, 31)) & (months.between(1, 12)) & (years.str.len() == 4)).all():
                result = (False, "Invalid date values in DD/MM/YYYY format")
                self._validation_cache[cache_key] = result
                return result
        except Exception as e:
            result = (False, f"Invalid date format in 'Date' column. Use DD/MM/YYYY format. Error: {str(e)}")
            self._validation_cache[cache_key] = result
            return result
        
        result = (True, "Settings data is valid")
        self._validation_cache[cache_key] = result
        return result
    
    def validate_goods_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate goods CSV data with optimized performance"""
        # Cache validation results
        cache_key = hash(str(df))
        if hasattr(self, '_validation_cache') and cache_key in self._validation_cache:
            return self._validation_cache[cache_key]
        
        # Check for required columns using set operations
        missing_columns = set(self.required_goods_columns) - set(df.columns)
        if missing_columns:
            result = (False, f"Missing required columns: {', '.join(missing_columns)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Use vectorized operations for empty value checks
        empty_cols = [col for col in self.required_goods_columns if df[col].isna().any()]
        if empty_cols:
            result = (False, f"Columns with empty values: {', '.join(empty_cols)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize numeric validation using vectorized operations
        try:
            numeric_cols = ['Quantity', 'Gross weight', 'FOB value']
            df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
            
            # Check for positive numbers
            invalid_nums = df[numeric_cols].le(0).any()
            if invalid_nums.any():
                invalid_cols = invalid_nums[invalid_nums].index.tolist()
                result = (False, f"Values must be greater than 0 in columns: {', '.join(invalid_cols)}")
                self._validation_cache[cache_key] = result
                return result
        except Exception as e:
            result = (False, f"Invalid number format in numeric columns: {str(e)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize date validation using vectorized operations
        try:
            date_pattern = r'^(\d{2})/(\d{2})/(\d{4})$'
            date_mask = df['Invoice Date'].str.match(date_pattern)
            if not date_mask.all():
                result = (False, "Invoice Date must be in DD/MM/YYYY format")
                self._validation_cache[cache_key] = result
                return result
            
            # Extract date components efficiently
            dates = df['Invoice Date'].str.extract(date_pattern)
            days = pd.to_numeric(dates[0])
            months = pd.to_numeric(dates[1])
            years = pd.to_numeric(dates[2])
            
            # Validate date components
            if not ((days.between(1, 31)) & (months.between(1, 12)) & (years.str.len() == 4)).all():
                result = (False, "Invalid date values in Invoice Date")
                self._validation_cache[cache_key] = result
                return result
        except Exception as e:
            result = (False, f"Invalid date format in 'Invoice Date' column: {str(e)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize Origin Criteria validation
        valid_criteria = {'CC', 'CTH', 'CTSH', 'CTC', 'PSR', 'WO'}
        invalid_criteria = set(df['Origin Criteria']) - valid_criteria
        if invalid_criteria:
            result = (False, f"Invalid Origin Criteria values: {', '.join(invalid_criteria)}")
            self._validation_cache[cache_key] = result
            return result
        
        # Optimize PackageQuantity validation if present
        if 'PackageQuantity' in df.columns and not df['PackageQuantity'].isna().all():
            try:
                df['PackageQuantity'] = pd.to_numeric(df['PackageQuantity'].dropna())
                if (df['PackageQuantity'] <= 0).any():
                    result = (False, "PackageQuantity must be greater than 0")
                    self._validation_cache[cache_key] = result
                    return result
            except Exception as e:
                result = (False, f"Invalid number format in 'PackageQuantity' column: {str(e)}")
                self._validation_cache[cache_key] = result
                return result
        
        result = (True, "Goods data is valid")
        self._validation_cache[cache_key] = result
        return result
    
    def show_error_log(self, error_message: str):
        """Show error log dialog"""
        self.error_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_message}")
        dialog = ErrorLogDialog("\n".join(self.error_log), self)
        dialog.exec_()

    def import_settings(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Settings CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                # Use pandas optimized reading with caching
                cache_key = hash(file_name)
                if hasattr(self, '_settings_cache') and cache_key in self._settings_cache:
                    self.settings_data = self._settings_cache[cache_key]
                else:
                    # Optimize CSV reading with specific dtypes and parsing dates
                    self.settings_data = pd.read_csv(
                        file_name,
                        dtype={
                            'Batch No': str,
                            'Form Value': str,
                            'Importing Country': str,
                            'Export Declaration Number': str,
                            'Transportation type': str,
                            'Port of Loading': str,
                            'Port of Discharge': str,
                            'Consignee\'s name': str,
                            'Address line 1': str,
                            'Vessel\'s Name/Aircraft etc': str
                        },
                        parse_dates=['Date', 'Departure date'],
                        date_parser=lambda x: pd.to_datetime(x, format='%d/%m/%Y')
                    )
                    self._settings_cache[cache_key] = self.settings_data
                
                # Validate the data
                is_valid, message = self.validate_settings_csv(self.settings_data)
                if not is_valid:
                    self.show_error_log(f"Settings Validation Error: {message}")
                    self.settings_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.settings_data, "Settings Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing settings: {str(e)}")
                self.settings_data = None

    def import_goods(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Goods CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                # Use pandas optimized reading with caching
                cache_key = hash(file_name)
                if hasattr(self, '_goods_cache') and cache_key in self._goods_cache:
                    self.goods_data = self._goods_cache[cache_key]
                else:
                    # Optimize CSV reading with specific dtypes and parsing dates
                    self.goods_data = pd.read_csv(
                        file_name,
                        dtype={
                            'Batch No': str,
                            'Exporting HS code': str,
                            'Goods description': str,
                            'Quantity': float,
                            'Quantity Unit': str,
                            'Gross weight': float,
                            'Gross weight unit': str,
                            'Invoice number': str,
                            'Mark and number on package': str,
                            'FOB value': float,
                            'Currency': str,
                            'Origin Criteria': str,
                            'PackageQuantity': float,
                            'PackageQuantityUnit': str
                        },
                        parse_dates=['Invoice Date'],
                        date_parser=lambda x: pd.to_datetime(x, format='%d/%m/%Y')
                    )
                    
                    # Ensure invoice numbers are strings with leading zeros
                    max_invoice_length = len(self.goods_data['Invoice number'].astype(str).max())
                    self.goods_data['Invoice number'] = self.goods_data['Invoice number'].astype(str).str.zfill(max_invoice_length)
                    
                    self._goods_cache[cache_key] = self.goods_data
                
                # Validate the data
                is_valid, message = self.validate_goods_csv(self.goods_data)
                if not is_valid:
                    self.show_error_log(f"Goods Validation Error: {message}")
                    self.goods_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.goods_data, "Goods Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing goods: {str(e)}")
                self.goods_data = None
    
    def start_background_task(self, task, settings_data=None, goods_data=None):
        """Start a background task with the automation worker"""
        if self.worker is None or not self.worker.isRunning():
            if self.worker is None:
                self.worker = AutomationWorker(self)
                self.worker.finished.connect(self.on_worker_finished)
                self.worker.error.connect(self.on_worker_error)
                self.worker.progress.connect(self.on_worker_progress)
            
            self.worker.set_task(task, settings_data, goods_data)
            self.worker.start()
        else:
            self.show_error_log("Previous automation task is still running")

    def on_worker_progress(self, message):
        """Handle progress updates from the worker"""
        print(f"Progress: {message}")

    def on_worker_finished(self):
        """Handle completion of the worker task"""
        print("Automation task completed successfully")

    def on_worker_error(self, error_message):
        """Handle errors from the worker"""
        self.show_error_log(error_message)

    def start_co(self):
        self.start_background_task("start_co_creation")
    
    def fill_co_form(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)
    
    def fill_goods(self):
        self.start_background_task("fill_goods", goods_data=self.goods_data)
    
    def fill_all(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)

    def show_settings_data(self):
        """Show current settings data in a popup"""
        if self.settings_data is not None:
            preview = DataPreviewDialog(self.settings_data, "Current Settings Data", self)
            preview.exec_()
        else:
            self.show_error_log("No C/O Form Information available. Please import data first.")
    
    def show_goods_data(self):
        """Show current goods data in a popup"""
        if self.goods_data is not None:
            preview = DataPreviewDialog(self.goods_data, "Current Goods Data", self)
            preview.exec_()
        else:
            self.show_error_log("No Goods Data available. Please import data first.")

    def closeEvent(self, event):
        """Handle application close event"""
        try:
            # Stop the automation worker if it's running
            if self.worker and self.worker.isRunning():
                self.worker.stop()
                self.worker.wait()
            
            # Close the Chrome driver
            if self.worker and self.worker.driver:
                self.worker.driver.quit()
        except Exception as e:
            print(f"Error closing Chrome driver: {str(e)}")
        event.accept()

    def fill_co_form_manual(self):
        """Fill CO form for a selected batch"""
        if self.settings_data is None:
            self.show_error_log("No C/O Form Information available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.settings_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_settings = self.settings_data[self.settings_data['Batch No'] == selected_batch]
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch] if self.goods_data is not None else None
            
            # Start the automation task
            self.start_background_task("fill_form", batch_settings, batch_goods)
    
    def fill_goods_manual(self):
        """Fill goods for a selected batch"""
        if self.goods_data is None:
            self.show_error_log("No Goods Data available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.goods_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch]
            
            # Start the automation task
            self.start_background_task("fill_goods", goods_data=batch_goods)

    def on_batch_completed(self, message):
        """Handle batch completion updates"""
        self.progress_text.append(message)
        self.progress_text.verticalScrollBar().setValue(
            self.progress_text.verticalScrollBar().maximum()
        )

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
