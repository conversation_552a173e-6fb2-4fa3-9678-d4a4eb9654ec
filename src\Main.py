import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QTabWidget, 
                           QWidget, QVBoxLayout, QPushButton, QTableWidget,
                           QTableWidgetItem, QFileDialog, QMessageBox, QLabel,
                           QCheckBox, QGroupBox, QHBoxLayout, QDialog, QScrollArea,
                           QTextEdit, QLineEdit, QFormLayout, QSpinBox, QComboBox)
from PySide6.QtCore import Qt, QThread, Signal
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import os
import configparser
from datetime import datetime
import time

import selenium_automation

class DataPreviewDialog(QDialog):
    def __init__(self, data: pd.DataFrame, title: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setGeometry(100, 100, 1200, 600)  # Increased width to 1200
        
        layout = QVBoxLayout(self)
        
        # Create table
        table = QTableWidget()
        table.setRowCount(len(data))
        table.setColumnCount(len(data.columns))
        table.setHorizontalHeaderLabels(data.columns)
        
        # Set header height to double
        header = table.horizontalHeader()
        header.setDefaultSectionSize(header.defaultSectionSize() * 2)
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #2196F3;
                color: white;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # Enable sorting
        table.setSortingEnabled(True)
        
        # Fill data without text wrapping
        for i in range(len(data)):
            for j in range(len(data.columns)):
                item = QTableWidgetItem(str(data.iloc[i, j]))
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                table.setItem(i, j, item)
        
        # Set fixed row height
        table.verticalHeader().setDefaultSectionSize(30)
        
        # Adjust column widths
        table.resizeColumnsToContents()
        
        # Add scroll area
        scroll = QScrollArea()
        scroll.setWidget(table)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # Add close button
        close_btn = QPushButton("Close")
        close_btn.setFixedHeight(30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

class ErrorLogDialog(QDialog):
    def __init__(self, error_message: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Error Log")
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setGeometry(100, 100, 600, 200)
        
        layout = QVBoxLayout(self)
        
        # Create text area for error message
        error_text = QTextEdit()
        error_text.setReadOnly(True)
        error_text.setPlainText(error_message)
        error_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(error_text)
        
        # Add close button
        close_btn = QPushButton("Close")
        close_btn.setFixedHeight(30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

class AutomationWorker(QThread):
    finished = Signal()
    error = Signal(str)
    progress = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.automation = None
        self.settings_data = None
        self.goods_data = None
        self.task = None
        self.driver = None
        self.is_running = False

    def set_task(self, task, settings_data=None, goods_data=None):
        self.task = task
        self.settings_data = settings_data
        self.goods_data = goods_data

    def run(self):
        try:
            self.is_running = True
            
            # Initialize Chrome driver if needed
            if self.driver is None:
                self._initialize_driver()
            
            if self.task == "start_co_creation":
                # Check and recover browser session if needed
                if not self._recover_browser_session():
                    self.error.emit("Failed to recover browser session. Please restart the application.")
                    return
                
                self.automation.start_co()
                self.finished.emit()
            
            elif self.task == "fill_form":
                if self.settings_data is None:
                    raise ValueError("Settings data is required for form filling")
                
                # Get unique batch numbers from settings data
                batch_numbers = self.settings_data['Batch No'].unique()
                
                for batch_no in batch_numbers:
                    if not self.is_running:
                        break
                        
                    try:
                        # Check and recover browser session if needed
                        if not self._recover_browser_session():
                            self.error.emit("Failed to recover browser session. Please restart the application.")
                            return
                        
                        # Filter settings data for current batch
                        batch_settings = self.settings_data[self.settings_data['Batch No'] == batch_no].iloc[0]
                        
                        # Filter goods data for current batch
                        batch_goods = self.goods_data[self.goods_data['Batch No'] == batch_no] if self.goods_data is not None else None
                        
                        # Fill CO form for current batch
                        self.automation.fill_co_form(batch_settings)
                        self.progress.emit(f"Filled CO form for batch {batch_no}")
                        
                        # Fill goods for current batch if available
                        if batch_goods is not None and not batch_goods.empty:
                            self.automation.fill_goods(batch_goods)
                            self.progress.emit(f"Filled goods for batch {batch_no}")
                        
                        # After completing the batch, click the Khai báo C/O link
                        try:
                            khai_bao_link = WebDriverWait(self.driver, 15).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, "a.rtIn[href='/CertificatesUpgrade/Business/CertificateEdit.aspx']"))
                            )
                            khai_bao_link.click()
                            time.sleep(3)  # Wait for the new page to load
                            self.progress.emit(f"Opened new CO form for next batch")
                        except Exception as e:
                            self.error.emit(f"Error opening new CO form: {str(e)}")
                            continue
                        
                        # Wait between batches
                        time.sleep(2)
                        
                    except Exception as e:
                        self.error.emit(f"Error processing batch {batch_no}: {str(e)}")
                        continue
                
                self.finished.emit()
            
            elif self.task == "fill_goods":
                if self.goods_data is None:
                    raise ValueError("Goods data is required for goods filling")
                
                # Get unique batch numbers from goods data
                batch_numbers = self.goods_data['Batch No'].unique()
                
                for batch_no in batch_numbers:
                    if not self.is_running:
                        break
                        
                    try:
                        # Filter goods data for current batch
                        batch_goods = self.goods_data[self.goods_data['Batch No'] == batch_no]
                        
                        # Fill goods for current batch
                        self.automation.fill_goods(batch_goods)
                        self.progress.emit(f"Filled goods for batch {batch_no}")
                        
                        # After completing the batch, click the Khai báo C/O link
                        try:
                            khai_bao_link = WebDriverWait(self.driver, 15).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, "a.rtIn[href='/CertificatesUpgrade/Business/CertificateEdit.aspx']"))
                            )
                            khai_bao_link.click()
                            time.sleep(3)  # Wait for the new page to load
                            self.progress.emit(f"Opened new CO form for next batch")
                        except Exception as e:
                            self.error.emit(f"Error opening new CO form: {str(e)}")
                            continue
                        
                        # Wait between batches
                        time.sleep(2)
                        
                    except Exception as e:
                        self.error.emit(f"Error processing batch {batch_no}: {str(e)}")
                        continue
                
                self.finished.emit()
            
        except Exception as e:
            self.error.emit(str(e))
        finally:
            self.is_running = False
            # Only close the browser if it's the last task
            if self.task == "fill_goods" and self.driver:
                self.driver.quit()

    def stop(self):
        """Stop the automation process"""
        self.is_running = False

    def _initialize_driver(self):
        try:
            chrome_options = Options()
            chrome_options.add_argument('--start-maximized')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_argument('--disable-infobars')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            
            # Add user agent to avoid detection
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36')
            
            # Get the path to the Chrome driver in src folder
            if getattr(sys, 'frozen', False):
                # If running as executable
                application_path = os.path.dirname(sys.executable)
                driver_path = os.path.join(application_path, 'src', 'Chrome Driver', 'chromedriver.exe')
            else:
                # If running in development
                driver_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Chrome Driver', 'chromedriver.exe')
            
            if not os.path.exists(driver_path):
                raise FileNotFoundError(f"Chrome driver not found at: {driver_path}")
            
            service = Service(driver_path)
            
            # Initialize driver with retry mechanism
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    # Set page load timeout
                    self.driver.set_page_load_timeout(30)
                    # Set script timeout
                    self.driver.set_script_timeout(30)
                    # Set implicit wait
                    self.driver.implicitly_wait(10)
                    break
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise Exception(f"Failed to initialize Chrome driver after {max_retries} attempts: {str(e)}")
                    time.sleep(2)  # Wait before retry
            
            self.automation = selenium_automation.COAutomation(self.driver)
            
        except Exception as e:
            self.error.emit(f"Failed to initialize Chrome driver: {str(e)}")
            return

    def _recover_browser_session(self):
        """Attempt to recover the browser session if it's invalid"""
        try:
            # Check if browser is still responsive
            try:
                self.driver.current_url
                return True
            except:
                # Browser session is invalid, try to recover
                try:
                    # Close the existing driver if it exists
                    if self.driver:
                        try:
                            self.driver.quit()
                        except:
                            pass
                    
                    # Reinitialize the driver
                    self._initialize_driver()
                    return True
                except Exception as e:
                    self.error.emit(f"Failed to recover browser session: {str(e)}")
                    return False
        except Exception as e:
            self.error.emit(f"Error checking browser session: {str(e)}")
            return False

class SettingsTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.settings_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        'config', 'settings.ini')
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # Reduced margins
        
        # Create form layout for settings
        form_layout = QFormLayout()
        form_layout.setSpacing(2)  # Reduced spacing
        
        # URL Settings
        url_group = QGroupBox("URL Settings")
        url_layout = QFormLayout()
        url_layout.setSpacing(2)
        self.base_url = QLineEdit()
        url_layout.addRow("Base URL:", self.base_url)
        url_group.setLayout(url_layout)
        
        # Credentials
        cred_group = QGroupBox("Credentials")
        cred_layout = QFormLayout()
        cred_layout.setSpacing(2)
        self.username = QLineEdit()
        self.password = QLineEdit()
        self.password.setEchoMode(QLineEdit.Password)
        cred_layout.addRow("Username:", self.username)
        cred_layout.addRow("Password:", self.password)
        cred_group.setLayout(cred_layout)
        
        # Path Settings
        path_group = QGroupBox("Path Settings")
        path_layout = QFormLayout()
        path_layout.setSpacing(2)
        self.chrome_driver = QLineEdit()
        self.download_dir = QLineEdit()
        path_layout.addRow("Chrome Driver:", self.chrome_driver)
        path_layout.addRow("Download Directory:", self.download_dir)
        path_group.setLayout(path_layout)
        
        # Browser Settings
        browser_group = QGroupBox("Browser Settings")
        browser_layout = QFormLayout()
        browser_layout.setSpacing(2)
        self.headless = QCheckBox()
        self.timeout = QSpinBox()
        self.timeout.setRange(10, 120)
        self.timeout.setValue(30)
        browser_layout.addRow("Headless Mode:", self.headless)
        browser_layout.addRow("Timeout (seconds):", self.timeout)
        browser_group.setLayout(browser_layout)
        
        # Add all groups to main layout
        layout.addWidget(url_group)
        layout.addWidget(cred_group)
        layout.addWidget(path_group)
        layout.addWidget(browser_group)
        
        # Add save button
        save_btn = QPushButton("Save Settings")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                min-width: 100px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        # Add stretch to push everything up
        layout.addStretch()

    def load_settings(self):
        try:
            config = configparser.ConfigParser()
            config.read(self.settings_path)
            
            # Load URL settings
            self.base_url.setText(config.get('URL', 'base_url', fallback=''))
            
            # Load credentials
            self.username.setText(config.get('Credentials', 'username', fallback=''))
            self.password.setText(config.get('Credentials', 'password', fallback=''))
            
            # Load path settings
            self.chrome_driver.setText(config.get('Paths', 'chrome_driver', fallback='auto'))
            self.download_dir.setText(config.get('Paths', 'download_dir', fallback='downloads'))
            
            # Load browser settings
            self.headless.setChecked(config.getboolean('Settings', 'headless', fallback=False))
            self.timeout.setValue(config.getint('Settings', 'timeout', fallback=30))
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load settings: {str(e)}")

    def save_settings(self):
        try:
            if getattr(sys, 'frozen', False):
                # When running as executable, save to the executable's directory
                save_path = os.path.join(os.path.dirname(sys.executable), 'config', 'settings.ini')
            else:
                save_path = self.settings_path

            # Ensure config directory exists
            config_dir = os.path.dirname(save_path)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            config = configparser.ConfigParser()
            
            # Save URL settings
            config['URL'] = {
                'base_url': self.base_url.text()
            }
            
            # Save credentials
            config['Credentials'] = {
                'username': self.username.text(),
                'password': self.password.text()
            }
            
            # Path settings
            config['Paths'] = {
                'chrome_driver': self.chrome_driver.text(),
                'download_dir': self.download_dir.text()
            }
            
            # Browser settings
            config['Settings'] = {
                'headless': str(self.headless.isChecked()).lower(),
                'timeout': str(self.timeout.value())
            }
            
            # Save to file
            with open(save_path, 'w') as f:
                config.write(f)
            
            # Reload settings in main window
            if self.parent:
                self.parent.load_settings()
            
            QMessageBox.information(self, "Success", "Settings saved successfully!")
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to save settings: {str(e)}")

    def validate_settings_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate settings CSV data"""
        # Check for required columns
        missing_columns = [col for col in self.required_settings_columns if col not in df.columns]
        if missing_columns:
            return False, f"Missing required columns: {', '.join(missing_columns)}"
        
        # Check for empty values in required columns
        for col in self.required_settings_columns:
            if df[col].isna().any():
                return False, f"Column '{col}' contains empty values"
        
        # Check for duplicate Batch No
        if df['Batch No'].duplicated().any():
            return False, "Batch No must be unique in settings CSV"
        
        # Validate date format (DD/MM/YYYY) for Date
        try:
            for date_str in df['Date']:
                if not isinstance(date_str, str):
                    return False, "Date must be a string in DD/MM/YYYY format"
                parts = date_str.split('/')
                if len(parts) != 3:
                    return False, "Date must be in DD/MM/YYYY format"
                day, month, year = parts
                if not (day.isdigit() and month.isdigit() and year.isdigit()):
                    return False, "Date must contain only numbers and forward slashes"
                if not (1 <= int(day) <= 31 and 1 <= int(month) <= 12 and len(year) == 4):
                    return False, "Invalid date values in DD/MM/YYYY format"
        except Exception as e:
            return False, f"Invalid date format in 'Date' column. Use DD/MM/YYYY format. Error: {str(e)}"
        
        # Validate date format (DD/MM/YYYY) for Departure date
        try:
            for date_str in df['Departure date']:
                if not isinstance(date_str, str):
                    return False, "Departure date must be a string in DD/MM/YYYY format"
                parts = date_str.split('/')
                if len(parts) != 3:
                    return False, "Departure date must be in DD/MM/YYYY format"
                day, month, year = parts
                if not (day.isdigit() and month.isdigit() and year.isdigit()):
                    return False, "Departure date must contain only numbers and forward slashes"
                if not (1 <= int(day) <= 31 and 1 <= int(month) <= 12 and len(year) == 4):
                    return False, "Invalid date values in Departure date format"
        except Exception as e:
            return False, f"Invalid date format in 'Departure date' column. Use DD/MM/YYYY format. Error: {str(e)}"
        
        return True, "Settings data is valid"
    
    def validate_goods_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate goods CSV data"""
        # Check for required columns
        missing_columns = [col for col in self.required_goods_columns if col not in df.columns]
        if missing_columns:
            return False, f"Missing required columns: {', '.join(missing_columns)}"
        
        # Check for empty values in required columns
        for col in self.required_goods_columns:
            if df[col].isna().any():
                return False, f"Column '{col}' contains empty values"
        
        # Convert invoice number to string to preserve leading zeros
        df['Invoice number'] = df['Invoice number'].astype(str)
        
        # Validate numeric fields
        try:
            # Convert numeric columns
            df['Quantity'] = pd.to_numeric(df['Quantity'])
            df['Gross weight'] = pd.to_numeric(df['Gross weight'])
            df['FOB value'] = pd.to_numeric(df['FOB value'])
            
            # Validate positive numbers
            if (df['Quantity'] <= 0).any():
                return False, "Quantity must be greater than 0"
            if (df['Gross weight'] <= 0).any():
                return False, "Gross weight must be greater than 0"
            if (df['FOB value'] <= 0).any():
                return False, "FOB value must be greater than 0"
        except:
            return False, "Invalid number format in numeric columns (Quantity, Gross weight, FOB value)"
        
        # Validate date format (DD/MM/YYYY) for Invoice Date
        try:
            for date_str in df['Invoice Date']:
                if not isinstance(date_str, str):
                    return False, "Invoice Date must be a string in DD/MM/YYYY format"
                parts = date_str.split('/')
                if len(parts) != 3:
                    return False, "Invoice Date must be in DD/MM/YYYY format"
                day, month, year = parts
                if not (day.isdigit() and month.isdigit() and year.isdigit()):
                    return False, "Invoice Date must contain only numbers and forward slashes"
                if not (1 <= int(day) <= 31 and 1 <= int(month) <= 12 and len(year) == 4):
                    return False, "Invalid date values in Invoice Date"
        except Exception as e:
            return False, f"Invalid date format in 'Invoice Date' column. Use DD/MM/YYYY format. Error: {str(e)}"
        
        # Validate Origin Criteria
        valid_criteria = ['CC', 'CTH', 'CTSH', 'CTC', 'PSR', 'WO']
        if not df['Origin Criteria'].isin(valid_criteria).all():
            return False, f"Origin Criteria must be one of: {', '.join(valid_criteria)}"
        
        # Validate optional PackageQuantity if present
        if 'PackageQuantity' in df.columns and not df['PackageQuantity'].isna().all():
            try:
                df['PackageQuantity'] = pd.to_numeric(df['PackageQuantity'].dropna())
                if (df['PackageQuantity'] <= 0).any():
                    return False, "PackageQuantity must be greater than 0"
            except:
                return False, "Invalid number format in 'PackageQuantity' column"
        
        return True, "Goods data is valid"
    
    def show_error_log(self, error_message: str):
        """Show error log dialog"""
        self.error_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_message}")
        dialog = ErrorLogDialog("\n".join(self.error_log), self)
        dialog.exec_()

    def import_settings(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Settings CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                self.settings_data = pd.read_csv(file_name)
                
                # Validate the data
                is_valid, message = self.validate_settings_csv(self.settings_data)
                if not is_valid:
                    self.show_error_log(f"Settings Validation Error: {message}")
                    self.settings_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.settings_data, "Settings Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing settings: {str(e)}")
                self.settings_data = None
    
    def import_goods(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Goods CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                # Read CSV with invoice number as string to preserve leading zeros
                self.goods_data = pd.read_csv(file_name, dtype={'Invoice number': str})
                
                # Ensure invoice numbers are strings with leading zeros
                self.goods_data['Invoice number'] = self.goods_data['Invoice number'].astype(str).str.zfill(len(self.goods_data['Invoice number'].astype(str).max()))
                
                # Validate the data
                is_valid, message = self.validate_goods_csv(self.goods_data)
                if not is_valid:
                    self.show_error_log(f"Goods Validation Error: {message}")
                    self.goods_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.goods_data, "Goods Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing goods: {str(e)}")
                self.goods_data = None
    
    def start_background_task(self, task, settings_data=None, goods_data=None):
        """Start a background task with the automation worker"""
        if self.worker is None or not self.worker.isRunning():
            if self.worker is None:
                self.worker = AutomationWorker(self)
                self.worker.finished.connect(self.on_worker_finished)
                self.worker.error.connect(self.on_worker_error)
                self.worker.progress.connect(self.on_worker_progress)
            
            self.worker.set_task(task, settings_data, goods_data)
            self.worker.start()
        else:
            self.show_error_log("Previous automation task is still running")

    def on_worker_progress(self, message):
        """Handle progress updates from the worker"""
        print(f"Progress: {message}")

    def on_worker_finished(self):
        """Handle completion of the worker task"""
        print("Automation task completed successfully")

    def on_worker_error(self, error_message):
        """Handle errors from the worker"""
        self.show_error_log(error_message)

    def start_co(self):
        self.start_background_task("start_co_creation")
    
    def fill_co_form(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)
    
    def fill_goods(self):
        self.start_background_task("fill_goods", goods_data=self.goods_data)
    
    def fill_all(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)

    def show_settings_data(self):
        """Show current settings data in a popup"""
        if self.settings_data is not None:
            preview = DataPreviewDialog(self.settings_data, "Current Settings Data", self)
            preview.exec_()
        else:
            self.show_error_log("No C/O Form Information available. Please import data first.")
    
    def show_goods_data(self):
        """Show current goods data in a popup"""
        if self.goods_data is not None:
            preview = DataPreviewDialog(self.goods_data, "Current Goods Data", self)
            preview.exec_()
        else:
            self.show_error_log("No Goods Data available. Please import data first.")

    def closeEvent(self, event):
        """Handle application close event"""
        try:
            # Stop the automation worker if it's running
            if self.worker and self.worker.isRunning():
                self.worker.stop()
                self.worker.wait()
            
            # Close the Chrome driver
            if self.worker and self.worker.driver:
                self.worker.driver.quit()
        except Exception as e:
            print(f"Error closing Chrome driver: {str(e)}")
        event.accept()

    def fill_co_form_manual(self):
        """Fill CO form for a selected batch"""
        if self.settings_data is None:
            self.show_error_log("No C/O Form Information available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.settings_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_settings = self.settings_data[self.settings_data['Batch No'] == selected_batch]
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch] if self.goods_data is not None else None
            
            # Start the automation task
            self.start_background_task("fill_form", batch_settings, batch_goods)
    
    def fill_goods_manual(self):
        """Fill goods for a selected batch"""
        if self.goods_data is None:
            self.show_error_log("No Goods Data available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.goods_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch]
            
            # Start the automation task
            self.start_background_task("fill_goods", goods_data=batch_goods)

class BatchSelectionDialog(QDialog):
    def __init__(self, batch_numbers, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Batch Number")
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setGeometry(100, 100, 300, 150)
        
        layout = QVBoxLayout(self)
        
        # Create combo box for batch selection
        self.batch_combo = QComboBox()
        self.batch_combo.addItems(batch_numbers)
        self.batch_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(img/down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(QLabel("Select Batch Number:"))
        layout.addWidget(self.batch_combo)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        ok_btn = QPushButton("OK")
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        ok_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def get_selected_batch(self):
        return self.batch_combo.currentText()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # Update settings path handling
        if getattr(sys, 'frozen', False):
            # If running as executable
            self.settings_path = os.path.join(sys._MEIPASS, 'config', 'settings.ini')
        else:
            # If running in development
            self.settings_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                        'config', 'settings.ini')
        self.setWindowTitle("CO Automation Tool")
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        
        # Get screen geometry
        screen = QApplication.primaryScreen().geometry()
        # Set window size
        window_width = 270
        window_height = 270
        # Calculate position (100px from top, 100px from right edge)
        x = screen.width() - window_width - 100  # 100px from right edge
        y = 100  # 100px from top
        
        self.setGeometry(x, y, window_width, window_height)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(2)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        
        # Create main tabs
        data_import_tab = QWidget()
        data_import_layout = QVBoxLayout(data_import_tab)
        data_import_layout.setSpacing(2)
        
        # Common button style
        button_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 10px;
                border-radius: 3px;
                min-width: 180px;
                text-align: left;
                padding-left: 10px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """
        
        # C/O Form Information Frame
        co_form_group = QGroupBox("C/O Form Information")
        co_form_layout = QVBoxLayout()
        co_form_layout.setSpacing(2)
        
        import_settings_btn = QPushButton("Import C/O Form Information (CSV)")
        import_settings_btn.setFixedHeight(28)
        import_settings_btn.setStyleSheet(button_style)
        import_settings_btn.clicked.connect(self.import_settings)
        co_form_layout.addWidget(import_settings_btn)
        
        show_settings_btn = QPushButton("Show Current C/O Form Information")
        show_settings_btn.setFixedHeight(28)
        show_settings_btn.setStyleSheet(button_style)
        show_settings_btn.clicked.connect(self.show_settings_data)
        co_form_layout.addWidget(show_settings_btn)
        
        co_form_group.setLayout(co_form_layout)
        data_import_layout.addWidget(co_form_group)
        
        # Goods Data Frame
        goods_group = QGroupBox("Goods Data")
        goods_layout = QVBoxLayout()
        goods_layout.setSpacing(2)
        
        import_goods_btn = QPushButton("Import Goods (CSV)")
        import_goods_btn.setFixedHeight(28)
        import_goods_btn.setStyleSheet(button_style)
        import_goods_btn.clicked.connect(self.import_goods)
        goods_layout.addWidget(import_goods_btn)
        
        show_goods_btn = QPushButton("Show Current Goods")
        show_goods_btn.setFixedHeight(28)
        show_goods_btn.setStyleSheet(button_style)
        show_goods_btn.clicked.connect(self.show_goods_data)
        goods_layout.addWidget(show_goods_btn)
        
        goods_group.setLayout(goods_layout)
        data_import_layout.addWidget(goods_group)
        
        # Add stretch to push everything up
        data_import_layout.addStretch()
        
        # Create CO tab
        co_tab = QWidget()
        co_layout = QVBoxLayout(co_tab)
        co_layout.setSpacing(2)
        
        # Start Mode Frame
        start_mode_group = QGroupBox("Start Mode")
        start_mode_layout = QVBoxLayout()
        start_mode_layout.setSpacing(2)
        
        start_co_btn = QPushButton("Start Create CO")
        start_co_btn.setFixedHeight(28)
        start_co_btn.setStyleSheet(button_style)
        start_co_btn.clicked.connect(self.start_co)
        start_mode_layout.addWidget(start_co_btn)
        
        start_mode_group.setLayout(start_mode_layout)
        co_layout.addWidget(start_mode_group)
        
        # Automation Mode Frame
        auto_mode_group = QGroupBox("Automation Mode")
        auto_mode_layout = QVBoxLayout()
        auto_mode_layout.setSpacing(2)
        
        fill_all_btn = QPushButton("Fill CO Form and Goods")
        fill_all_btn.setFixedHeight(28)
        fill_all_btn.setStyleSheet(button_style)
        fill_all_btn.clicked.connect(self.fill_all)
        auto_mode_layout.addWidget(fill_all_btn)
        
        auto_mode_group.setLayout(auto_mode_layout)
        co_layout.addWidget(auto_mode_group)
        
        # Manual Mode Frame
        manual_mode_group = QGroupBox("Manual Mode")
        manual_mode_layout = QVBoxLayout()
        manual_mode_layout.setSpacing(2)
        
        fill_form_btn = QPushButton("Fill CO Form")
        fill_form_btn.setFixedHeight(28)
        fill_form_btn.setStyleSheet(button_style)
        fill_form_btn.clicked.connect(self.fill_co_form_manual)
        manual_mode_layout.addWidget(fill_form_btn)
        
        fill_goods_btn = QPushButton("Fill Goods")
        fill_goods_btn.setFixedHeight(28)
        fill_goods_btn.setStyleSheet(button_style)
        fill_goods_btn.clicked.connect(self.fill_goods_manual)
        manual_mode_layout.addWidget(fill_goods_btn)
        
        manual_mode_group.setLayout(manual_mode_layout)
        co_layout.addWidget(manual_mode_group)
        
        # Add stretch to push everything up
        co_layout.addStretch()
        
        # Create configuration tab
        config_tab = SettingsTab(self)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(data_import_tab, "Data Import")
        self.tab_widget.addTab(co_tab, "Certificate of Origin")
        self.tab_widget.addTab(config_tab, "Configuration")
        
        # Add tab widget to main layout
        layout.addWidget(self.tab_widget)
        
        # Initialize variables
        self.settings_data = None
        self.goods_data = None
        self.worker = None
        self.chrome_driver_path = None
        self.error_log = []
        
        # Define required columns for validation
        self.required_settings_columns = [
            'Batch No','Form Value', 'Importing Country', 'Export Declaration Number', 'Date',
            'Transportation type', 'Port of Loading', 'Port of Discharge',
            'Consignee\'s name', 'Address line 1', 'Vessel\'s Name/Aircraft etc',
            'Departure date'
        ]
        
        self.required_goods_columns = [
            'Batch No','Exporting HS code', 'Goods description', 'Quantity', 'Quantity Unit',
            'Gross weight', 'Gross weight unit', 'Invoice number', 'Invoice Date',
            'Mark and number on package', 'FOB value', 'Currency', 'Origin Criteria'
        ]
        
        # Define optional columns that should be validated if present
        self.optional_goods_columns = [
            'PackageQuantity', 'PackageQuantityUnit'
        ]
        
        # Load settings
        self.load_settings()
        
        # Set style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 4px 8px;
                margin-right: 1px;
                border: 1px solid #cccccc;
                border-bottom: none;
                font-size: 11px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: none;
            }
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 3px;
                margin-top: 4px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 4px;
                padding: 0 2px;
                font-size: 11px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                min-width: 100px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
            QLineEdit {
                padding: 2px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                font-size: 11px;
            }
            QSpinBox {
                padding: 2px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                font-size: 11px;
            }
            QCheckBox {
                spacing: 4px;
                font-size: 11px;
            }
            QLabel {
                font-size: 11px;
            }
            QFormLayout {
                spacing: 2px;
            }
        """)

    def load_settings(self):
        """Load settings from settings.ini file"""
        try:
            # First try to load from bundled settings
            if getattr(sys, 'frozen', False):
                bundled_settings = os.path.join(sys._MEIPASS, 'config', 'settings.ini')
                if os.path.exists(bundled_settings):
                    self.config = configparser.ConfigParser()
                    self.config.read(bundled_settings)
                else:
                    raise FileNotFoundError("Bundled settings.ini not found")
            else:
                # In development mode, use the original path
                if not os.path.exists(self.settings_path):
                    # Create default settings if file doesn't exist
                    self.create_default_settings()
                self.config = configparser.ConfigParser()
                self.config.read(self.settings_path)
            
            # Ensure all required sections exist
            required_sections = ['URL', 'Credentials', 'Paths', 'Settings']
            for section in required_sections:
                if section not in self.config:
                    self.config[section] = {}
            
            # Set default values if not present
            if 'base_url' not in self.config['URL']:
                self.config['URL']['base_url'] = 'https://ecosys.gov.vn/CertificatesUpgrade/Business/CertificateEdit.aspx'
            
            if 'username' not in self.config['Credentials']:
                self.config['Credentials']['username'] = ''
            if 'password' not in self.config['Credentials']:
                self.config['Credentials']['password'] = ''
            
            if 'chrome_driver' not in self.config['Paths']:
                self.config['Paths']['chrome_driver'] = 'auto'
            if 'download_dir' not in self.config['Paths']:
                self.config['Paths']['download_dir'] = 'downloads'
            
            if 'headless' not in self.config['Settings']:
                self.config['Settings']['headless'] = 'false'
            if 'timeout' not in self.config['Settings']:
                self.config['Settings']['timeout'] = '30'
            
            # Save any changes to the writable location if running as executable
            if getattr(sys, 'frozen', False):
                save_path = os.path.join(os.path.dirname(sys.executable), 'config', 'settings.ini')
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                with open(save_path, 'w') as configfile:
                    self.config.write(configfile)
                
        except Exception as e:
            msg = QMessageBox(self)
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.critical(self, "Error", f"Failed to load settings: {str(e)}")
            sys.exit(1)

    def create_default_settings(self):
        """Create default settings.ini file"""
        try:
            # Ensure config directory exists
            config_dir = os.path.dirname(self.settings_path)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            config = configparser.ConfigParser()
            
            config['URL'] = {
                'base_url': 'https://ecosys.gov.vn/CertificatesUpgrade/Business/CertificateEdit.aspx'
            }
            
            config['Credentials'] = {
                'username': '',
                'password': ''
            }
            
            config['Paths'] = {
                'chrome_driver': 'auto',
                'download_dir': 'downloads'
            }
            
            config['Settings'] = {
                'headless': 'false',
                'timeout': '30'
            }
            
            with open(self.settings_path, 'w') as configfile:
                config.write(configfile)
                
        except Exception as e:
            msg = QMessageBox(self)
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.critical(self, "Error", f"Failed to create default settings: {str(e)}")
            sys.exit(1)
    
    def validate_settings_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate settings CSV data"""
        # Check for required columns
        missing_columns = [col for col in self.required_settings_columns if col not in df.columns]
        if missing_columns:
            return False, f"Missing required columns: {', '.join(missing_columns)}"
        
        # Check for empty values in required columns
        for col in self.required_settings_columns:
            if df[col].isna().any():
                return False, f"Column '{col}' contains empty values"
        
        # Check for duplicate Batch No
        if df['Batch No'].duplicated().any():
            return False, "Batch No must be unique in settings CSV"
        
        # Validate date format (DD/MM/YYYY) for Date
        try:
            for date_str in df['Date']:
                if not isinstance(date_str, str):
                    return False, "Date must be a string in DD/MM/YYYY format"
                parts = date_str.split('/')
                if len(parts) != 3:
                    return False, "Date must be in DD/MM/YYYY format"
                day, month, year = parts
                if not (day.isdigit() and month.isdigit() and year.isdigit()):
                    return False, "Date must contain only numbers and forward slashes"
                if not (1 <= int(day) <= 31 and 1 <= int(month) <= 12 and len(year) == 4):
                    return False, "Invalid date values in DD/MM/YYYY format"
        except Exception as e:
            return False, f"Invalid date format in 'Date' column. Use DD/MM/YYYY format. Error: {str(e)}"
        
        # Validate date format (DD/MM/YYYY) for Departure date
        try:
            for date_str in df['Departure date']:
                if not isinstance(date_str, str):
                    return False, "Departure date must be a string in DD/MM/YYYY format"
                parts = date_str.split('/')
                if len(parts) != 3:
                    return False, "Departure date must be in DD/MM/YYYY format"
                day, month, year = parts
                if not (day.isdigit() and month.isdigit() and year.isdigit()):
                    return False, "Departure date must contain only numbers and forward slashes"
                if not (1 <= int(day) <= 31 and 1 <= int(month) <= 12 and len(year) == 4):
                    return False, "Invalid date values in Departure date format"
        except Exception as e:
            return False, f"Invalid date format in 'Departure date' column. Use DD/MM/YYYY format. Error: {str(e)}"
        
        return True, "Settings data is valid"
    
    def validate_goods_csv(self, df: pd.DataFrame) -> tuple[bool, str]:
        """Validate goods CSV data"""
        # Check for required columns
        missing_columns = [col for col in self.required_goods_columns if col not in df.columns]
        if missing_columns:
            return False, f"Missing required columns: {', '.join(missing_columns)}"
        
        # Check for empty values in required columns
        for col in self.required_goods_columns:
            if df[col].isna().any():
                return False, f"Column '{col}' contains empty values"
        
        # Convert invoice number to string to preserve leading zeros
        df['Invoice number'] = df['Invoice number'].astype(str)
        
        # Validate numeric fields
        try:
            # Convert numeric columns
            df['Quantity'] = pd.to_numeric(df['Quantity'])
            df['Gross weight'] = pd.to_numeric(df['Gross weight'])
            df['FOB value'] = pd.to_numeric(df['FOB value'])
            
            # Validate positive numbers
            if (df['Quantity'] <= 0).any():
                return False, "Quantity must be greater than 0"
            if (df['Gross weight'] <= 0).any():
                return False, "Gross weight must be greater than 0"
            if (df['FOB value'] <= 0).any():
                return False, "FOB value must be greater than 0"
        except:
            return False, "Invalid number format in numeric columns (Quantity, Gross weight, FOB value)"
        
        # Validate date format (DD/MM/YYYY) for Invoice Date
        try:
            for date_str in df['Invoice Date']:
                if not isinstance(date_str, str):
                    return False, "Invoice Date must be a string in DD/MM/YYYY format"
                parts = date_str.split('/')
                if len(parts) != 3:
                    return False, "Invoice Date must be in DD/MM/YYYY format"
                day, month, year = parts
                if not (day.isdigit() and month.isdigit() and year.isdigit()):
                    return False, "Invoice Date must contain only numbers and forward slashes"
                if not (1 <= int(day) <= 31 and 1 <= int(month) <= 12 and len(year) == 4):
                    return False, "Invalid date values in Invoice Date"
        except Exception as e:
            return False, f"Invalid date format in 'Invoice Date' column. Use DD/MM/YYYY format. Error: {str(e)}"
        
        # Validate Origin Criteria
        valid_criteria = ['CC', 'CTH', 'CTSH', 'CTC', 'PSR', 'WO']
        if not df['Origin Criteria'].isin(valid_criteria).all():
            return False, f"Origin Criteria must be one of: {', '.join(valid_criteria)}"
        
        # Validate optional PackageQuantity if present
        if 'PackageQuantity' in df.columns and not df['PackageQuantity'].isna().all():
            try:
                df['PackageQuantity'] = pd.to_numeric(df['PackageQuantity'].dropna())
                if (df['PackageQuantity'] <= 0).any():
                    return False, "PackageQuantity must be greater than 0"
            except:
                return False, "Invalid number format in 'PackageQuantity' column"
        
        return True, "Goods data is valid"
    
    def show_error_log(self, error_message: str):
        """Show error log dialog"""
        self.error_log.append(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_message}")
        dialog = ErrorLogDialog("\n".join(self.error_log), self)
        dialog.exec_()

    def import_settings(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Settings CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                self.settings_data = pd.read_csv(file_name)
                
                # Validate the data
                is_valid, message = self.validate_settings_csv(self.settings_data)
                if not is_valid:
                    self.show_error_log(f"Settings Validation Error: {message}")
                    self.settings_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.settings_data, "Settings Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing settings: {str(e)}")
                self.settings_data = None
    
    def import_goods(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Import Goods CSV", "", "CSV Files (*.csv)")
        if file_name:
            try:
                # Read CSV with invoice number as string to preserve leading zeros
                self.goods_data = pd.read_csv(file_name, dtype={'Invoice number': str})
                
                # Ensure invoice numbers are strings with leading zeros
                self.goods_data['Invoice number'] = self.goods_data['Invoice number'].astype(str).str.zfill(len(self.goods_data['Invoice number'].astype(str).max()))
                
                # Validate the data
                is_valid, message = self.validate_goods_csv(self.goods_data)
                if not is_valid:
                    self.show_error_log(f"Goods Validation Error: {message}")
                    self.goods_data = None
                    return
                
                # Show data preview
                preview = DataPreviewDialog(self.goods_data, "Goods Data Preview", self)
                preview.exec_()
                
            except Exception as e:
                self.show_error_log(f"Error importing goods: {str(e)}")
                self.goods_data = None
    
    def start_background_task(self, task, settings_data=None, goods_data=None):
        """Start a background task with the automation worker"""
        if self.worker is None or not self.worker.isRunning():
            if self.worker is None:
                self.worker = AutomationWorker(self)
                self.worker.finished.connect(self.on_worker_finished)
                self.worker.error.connect(self.on_worker_error)
                self.worker.progress.connect(self.on_worker_progress)
            
            self.worker.set_task(task, settings_data, goods_data)
            self.worker.start()
        else:
            self.show_error_log("Previous automation task is still running")

    def on_worker_progress(self, message):
        """Handle progress updates from the worker"""
        print(f"Progress: {message}")

    def on_worker_finished(self):
        """Handle completion of the worker task"""
        print("Automation task completed successfully")

    def on_worker_error(self, error_message):
        """Handle errors from the worker"""
        self.show_error_log(error_message)

    def start_co(self):
        self.start_background_task("start_co_creation")
    
    def fill_co_form(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)
    
    def fill_goods(self):
        self.start_background_task("fill_goods", goods_data=self.goods_data)
    
    def fill_all(self):
        self.start_background_task("fill_form", self.settings_data, self.goods_data)

    def show_settings_data(self):
        """Show current settings data in a popup"""
        if self.settings_data is not None:
            preview = DataPreviewDialog(self.settings_data, "Current Settings Data", self)
            preview.exec_()
        else:
            self.show_error_log("No C/O Form Information available. Please import data first.")
    
    def show_goods_data(self):
        """Show current goods data in a popup"""
        if self.goods_data is not None:
            preview = DataPreviewDialog(self.goods_data, "Current Goods Data", self)
            preview.exec_()
        else:
            self.show_error_log("No Goods Data available. Please import data first.")

    def closeEvent(self, event):
        """Handle application close event"""
        try:
            # Stop the automation worker if it's running
            if self.worker and self.worker.isRunning():
                self.worker.stop()
                self.worker.wait()
            
            # Close the Chrome driver
            if self.worker and self.worker.driver:
                self.worker.driver.quit()
        except Exception as e:
            print(f"Error closing Chrome driver: {str(e)}")
        event.accept()

    def fill_co_form_manual(self):
        """Fill CO form for a selected batch"""
        if self.settings_data is None:
            self.show_error_log("No C/O Form Information available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.settings_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_settings = self.settings_data[self.settings_data['Batch No'] == selected_batch]
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch] if self.goods_data is not None else None
            
            # Start the automation task
            self.start_background_task("fill_form", batch_settings, batch_goods)
    
    def fill_goods_manual(self):
        """Fill goods for a selected batch"""
        if self.goods_data is None:
            self.show_error_log("No Goods Data available. Please import data first.")
            return
        
        # Get unique batch numbers
        batch_numbers = self.goods_data['Batch No'].unique()
        
        # Show batch selection dialog
        dialog = BatchSelectionDialog(batch_numbers, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_batch = dialog.get_selected_batch()
            
            # Filter data for selected batch
            batch_goods = self.goods_data[self.goods_data['Batch No'] == selected_batch]
            
            # Start the automation task
            self.start_background_task("fill_goods", goods_data=batch_goods)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
