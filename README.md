# Certificate of Origin Automation Tool

This tool automates the process of filling out Certificate of Origin (C/O) forms using Selenium WebDriver.

## Project Structure

```
Upload CO Project/
├── src/                    # Source code files
│   ├── Main.py            # Main application GUI
│   └── selenium_automation.py  # Selenium automation logic
├── config/                 # Configuration files
│   └── settings.ini       # Application settings and credentials
├── data/                  # Data files
│   ├── co_form_sample.csv # Sample C/O form data
│   └── co_goods_sample.csv # Sample goods data
└── img/                   # Image assets
    └── (button images)    # UI button images
```

## Prerequisites

1. Python 3.8 or higher
2. Chrome browser installed
3. ChromeDriver matching your Chrome version
4. Required Python packages:
   - PySide6
   - pandas
   - selenium
   - webdriver_manager

## Setup Instructions

1. Clone or download this repository
2. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```
3. Configure settings.ini:
   Create a new file `config/settings.ini` with the following content:
   ```ini
   [URL]
   base_url = https://your_login_url
   
   [Credentials]
   username = your_username
   password = your_password

   [Paths]
   chrome_driver = auto  # Set to 'auto' for automatic ChromeDriver management
   download_dir = src # Directory for downloaded files src ub default

   [Settings]
   headless = false  # Set to true for headless browser operation
   timeout = 30  # Timeout in seconds for web elements
   ```
   
   Important: 
   - Create the settings.ini file before running the application
   - The file must be placed in the `config` directory
   - All sections and keys are required
   - For executable version, create settings.ini in the same directory as the executable

4. Prepare your data:
   - Copy `co_form_sample.csv` to create your C/O form data
   - Copy `co_goods_sample.csv` to create your goods data
   - Follow the format in the sample files

For Dev
   ```bash
   pyinstaller co_automation.spec #to complied the project

## Usage

1. Run the application:
   ```bash
   python src/Main.py
   ```
   Or if using the executable:
   - Double-click `CO_Automation.exe`
   - Ensure settings.ini is in the same directory

2. Configure Settings:
   - Use the "Settings" tab to view and edit application settings
   - Modify credentials, URLs, and other configuration options
   - Changes are saved automatically to settings.ini
   - The application will reload settings when changes are made

3. Import your data:
   - Click "Import C/O Form Information (CSV)" to import form data
   - Click "Import Goods (CSV)" to import goods data
   - Use "Show Current" buttons to preview imported data

4. Start automation:
   - Click "Start Create CO" to initialize the browser
   - Use "Fill CO Form" to fill the form
   - Use "Fill Goods" to fill goods information
   - Or use "Fill CO Form and Goods" to do both

## Data Format Requirements

### C/O Form Information (CSV)
Required columns:
- Form Value (1-99)
- Importing Country
- Export Declaration Number
- Date (DD/MM/YYYY)
- Transportation type
- Port of Loading
- Port of Discharge
- Consignee's name
- Address line 1
- Vessel's Name/Aircraft etc
- Departure date (DD/MM/YYYY)

### Goods Data (CSV)
Required columns:
- Exporting HS code
- Goods description
- Quantity
- Quantity Unit
- Gross weight
- Gross weight unit
- Invoice number
- Invoice Date (DD/MM/YYYY)
- Mark and number on package
- FOB value
- Currency
- Origin Criteria (CC, CTH, CTSH, CTC, PSR, WO)

Optional columns:
- PackageQuantity
- PackageQuantityUnit

## Error Handling

- All errors are logged in the Error Log dialog
- Validation errors are shown when importing data
- The application will show appropriate error messages for missing or invalid data
- If settings.ini is missing or invalid, the application will show an error message

## Troubleshooting

1. Settings.ini not found:
   - For source code: Create settings.ini in the config directory
   - For executable: Create settings.ini in the same directory as CO_Automation.exe
   - Use the template provided above
   - Make sure all required sections and keys are present

2. ChromeDriver issues:
   - Leave chrome_driver = auto in settings.ini for automatic management
   - The application will download the appropriate ChromeDriver version
   - If using a specific ChromeDriver, provide the full path in settings.ini

## Notes

- The application window stays on top for better visibility
- Data preview dialogs allow sorting and viewing of imported data
- ChromeDriver is managed automatically by default
- Make sure to close the browser properly when done
- Keep your settings.ini file secure as it contains sensitive information

## Notes

- The application window stays on top for better visibility
- Data preview dialogs allow sorting and viewing of imported data
- ChromeDriver must be installed and properly configured
- Make sure to close the browser properly when done 