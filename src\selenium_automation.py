from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time
import pandas as pd
import configparser
import os
import sys

class COAutomation:
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        # Load settings
        self.config = configparser.ConfigParser()
        if getattr(sys, 'frozen', False):
            # If running as executable
            settings_path = os.path.join(sys._MEIPASS, 'config', 'settings.ini')
        else:
            # If running in development
            settings_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.ini')
        self.config.read(settings_path)
    
    def start_co(self):
        """Start CO creation by navigating to the CO form and logging in"""
        try:
            # Check if browser is still responsive
            try:
                self.driver.current_url
            except:
                raise Exception("Browser session is no longer valid. Please restart the application.")

            # Navigate to the CO form with retry mechanism
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.driver.get(self.config['URL']['base_url'])
                    break
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise Exception(f"Failed to navigate to CO form after {max_retries} attempts: {str(e)}")
                    time.sleep(2)  # Wait before retry
            
            # Wait for page to be fully loaded
            time.sleep(3)
            
            # Login with retry mechanism
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Wait for login form to be present
                    login_form = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "ctl00_cplhContainer_txtLoginName"))
                    )
                    
                    # Clear fields first
                    login_form.clear()
                    password_field = self.driver.find_element(By.ID, "ctl00_cplhContainer_txtPassword")
                    password_field.clear()
                    
                    # Enter credentials
                    login_form.send_keys(self.config['Credentials']['username'])
                    password_field.send_keys(self.config['Credentials']['password'])
                    
                    # Set focus to captcha field
                    captcha_field = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "ctl00_cplhContainer_txtCaptcha"))
                    )
                    self.driver.execute_script("arguments[0].focus();", captcha_field)
                    break
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise Exception(f"Failed to enter login credentials after {max_retries} attempts: {str(e)}")
                    time.sleep(2)  # Wait before retry
            
        except Exception as e:
            print(f"Error starting CO creation: {str(e)}")
            raise
    
    def wait_and_find_element(self, by, value):
        return self.wait.until(EC.presence_of_element_located((by, value)))
    
    def fill_text_field(self, element_id, value):
        if not value:
            return
        element = self.wait_and_find_element(By.ID, element_id)
        
        # Special handling for Export Declaration Number
        if element_id == "ctl00_cplhContainer_plhCustomsNumber0_txtInvoiceNumber":
            # Clear using JavaScript to ensure complete clearing
            self.driver.execute_script("arguments[0].value = '';", element)
            time.sleep(1)
            # Send keys with a small delay between characters
            for char in str(value):
                element.send_keys(char)
                time.sleep(0.1)
        else:
            # Regular text field handling
            element.clear()
            element.send_keys(str(value))
        
        time.sleep(1)
    
    def select_dropdown(self, element_id, value, index=None, always_select_first=False):
        if not value:
            return
            
        try:
            # Special handling for Form dropdown, Importing Country dropdown, and Transportation type dropdown
            if element_id in ["ctl00_cplhContainer_cmbFormCO_Input", 
                            "ctl00_cplhContainer_cmbMarket_Input",
                            "ctl00_cplhContainer_cmbTransportMethod_Input"]:
                # Find the dropdown element
                element = self.wait_and_find_element(By.ID, element_id)
                
                # Click the dropdown to open it
                element.click()
                time.sleep(2)  # Increased wait time
                
                # Wait for the dropdown to be visible
                dropdown_container = self.wait_and_find_element(
                    By.CSS_SELECTOR, 
                    f"div[id*='{element_id.split('_Input')[0]}_DropDown']"
                )
                
                # Wait for the list to be visible
                dropdown_list = self.wait_and_find_element(
                    By.CSS_SELECTOR, 
                    f"div[id*='{element_id.split('_Input')[0]}_DropDown'] ul.rcbList"
                )
                
                # Find all options in the dropdown
                options = dropdown_list.find_elements(By.TAG_NAME, "li")
                
                # Convert value to integer and subtract 1 for zero-based index
                try:
                    form_index = int(value) - 1
                    if 0 <= form_index < len(options):
                        # Scroll the option into view
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", options[form_index])
                        time.sleep(1)
                        options[form_index].click()
                        time.sleep(1)
                        return
                except ValueError:
                    pass
                
                # If index is invalid or value is not a number, raise an error
                raise ValueError(f"Invalid value '{value}' for {element_id}. Please use a number between 1 and {len(options)}")
            
            # Special handling for dropdowns that need first suggestion selection
            elif element_id in ["ctl00_cplhContainer_cmbSenderPlace_Input", 
                              "ctl00_cplhContainer_cmbReceiverPlace_Input",
                              "ctl00_cplhContainer_cmbItemCurrencyUnit_Input",
                              "ctl00_cplhContainer_cmbBoxUnitId_Input",
                              "ctl00_cplhContainer_cmbHSCode_Input"]:  # Added Exporting HS code
                element = self.wait_and_find_element(By.ID, element_id)
                element.clear()
                element.click()
                time.sleep(1)
                element.send_keys(str(value))
                time.sleep(2)
                element.send_keys(Keys.ARROW_DOWN)
                time.sleep(1)
                element.send_keys(Keys.ENTER)
                time.sleep(1)
                return
            
            # Direct value entry for units
            elif element_id in ["ctl00_cplhContainer_cmbGwUnitId_Input", 
                              "ctl00_cplhContainer_cmbUnit_Input"]:
                element = self.wait_and_find_element(By.ID, element_id)
                element.clear()
                element.send_keys(str(value))
                element.send_keys(Keys.ENTER)
                time.sleep(1)
                return
            
            # Regular dropdown handling for other cases
            element = self.wait_and_find_element(By.ID, element_id)
            element.click()
            time.sleep(1)
            
            if always_select_first:
                # Send the value and wait for dropdown
                element.send_keys(str(value))
                time.sleep(1)
                # Find and click the first available option
                options = self.driver.find_elements(By.CSS_SELECTOR, f"#{element_id} option")
                if options:
                    options[0].click()
            elif index is not None:
                # For dropdowns where we need to select by index
                options = self.driver.find_elements(By.CSS_SELECTOR, f"#{element_id} option")
                if index < len(options):
                    options[index].click()
            else:
                # For dropdowns where we can type the value
                element.send_keys(str(value))
                element.send_keys(Keys.ENTER)
            
            time.sleep(1)
            
        except Exception as e:
            print(f"Error selecting dropdown {element_id}: {str(e)}")
            raise
    
    def fill_co_form(self, settings_data):
        """Fill the CO form with settings data"""
        try:
            # Form Value
            self.select_dropdown("ctl00_cplhContainer_cmbFormCO_Input", 
                               settings_data['Form Value'])
            
            # Importing Country
            self.select_dropdown("ctl00_cplhContainer_cmbMarket_Input", 
                               settings_data['Importing Country'])
            
            # Export Declaration Number
            self.fill_text_field("ctl00_cplhContainer_plhCustomsNumber0_txtInvoiceNumber",
                               settings_data['Export Declaration Number'])
            
            # Date
            self.fill_text_field("ctl00_cplhContainer_plhCustomsNumber0_radDpkInvoiceDate_dateInput",
                               settings_data['Date'])
            
            # Transportation type
            self.select_dropdown("ctl00_cplhContainer_cmbTransportMethod_Input", 
                               settings_data['Transportation type'])
            
            # Port of Loading
            self.select_dropdown("ctl00_cplhContainer_cmbSenderPlace_Input",
                               settings_data['Port of Loading'])
            
            # Port of Discharge
            self.select_dropdown("ctl00_cplhContainer_cmbReceiverPlace_Input",
                               settings_data['Port of Discharge'])
            
            # Consignee's name
            self.fill_text_field("ctl00_cplhContainer_PersionNameImportEnglish",
                               settings_data['Consignee\'s name'])
            
            # Address line 1
            self.fill_text_field("ctl00_cplhContainer_AddressEnglishImport",
                               settings_data['Address line 1'])
            
            # Address line 2 (optional)
            if 'Address line 2' in settings_data and settings_data['Address line 2']:
                self.fill_text_field("ctl00_cplhContainer_AddressEnglishImport2",
                                   settings_data['Address line 2'])
            
            # Vessel's Name/Aircraft etc
            self.fill_text_field("ctl00_cplhContainer_txtShipName",
                               settings_data['Vessel\'s Name/Aircraft etc'])
            
            # Departure date
            self.fill_text_field("ctl00_cplhContainer_txtTransportDate_dateInput",
                               settings_data['Departure date'])
            
        except Exception as e:
            print(f"Error filling CO form: {str(e)}")
            raise

    def fill_goods(self, goods_data):
        """Fill goods information"""
        # Click the Add/Update Item tab button first
        try:
            add_update_tab = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.ID, "ctl00_cplhContainer_radbtnSelectTabGoods_input"))
            )
            add_update_tab.click()
            time.sleep(3)  # Wait for the tab to be fully loaded
        except Exception as e:
            print(f"Warning: Could not click Add/Update Item tab: {str(e)}")
            raise

        for _, row in goods_data.iterrows():
            # Exporting HS code
            self.select_dropdown("ctl00_cplhContainer_cmbHSCode_Input",
                               row['Exporting HS code'],
                               always_select_first=True)
            
            # Goods description
            self.fill_text_field("ctl00_cplhContainer_txtName",
                               row['Goods description'])
            
            # Quantity
            self.fill_text_field("ctl00_cplhContainer_txtUnitValue",
                               row['Quantity'])
            
            # Quantity Unit
            self.select_dropdown("ctl00_cplhContainer_cmbUnit_Input",
                               row['Quantity Unit'],
                               always_select_first=True)
            
            # Gross weight
            self.fill_text_field("ctl00_cplhContainer_txtGwValue",
                               row['Gross weight'])
            
            # Gross weight unit
            self.select_dropdown("ctl00_cplhContainer_cmbGwUnitId_Input",
                               row['Gross weight unit'],
                               always_select_first=True)
            
            # Invoice number
            self.fill_text_field("ctl00_cplhContainer_txtInvoiceItem",
                               row['Invoice number'])
            
            # Invoice Date
            self.fill_text_field("ctl00_cplhContainer_radDpkInvoiceItemDate_dateInput",
                               row['Invoice Date'])
            
            # Mark and number on package
            self.fill_text_field("ctl00_cplhContainer_txtShippingMark",
                               row['Mark and number on package'])
            
            # Package Quantity - only fill if not null/blank
            if pd.notna(row['Package Quantity']) and str(row['Package Quantity']).strip():
                self.fill_text_field("ctl00_cplhContainer_txtBoxValue",
                                   row['Package Quantity'])
            
            # Package Quantity Unit - only fill if Package Quantity is not null/blank
            if pd.notna(row['Package Quantity']) and str(row['Package Quantity']).strip():
                self.select_dropdown("ctl00_cplhContainer_cmbBoxUnitId_Input",
                                   row['Package Quantity Unit'],
                                   always_select_first=True)
            
            # FOB value
            self.fill_text_field("ctl00_cplhContainer_txtCurrencyValue",
                               row['FOB value'])
            
            # Currency
            self.select_dropdown("ctl00_cplhContainer_cmbItemCurrencyUnit_Input",
                               row['Currency'])
            
            # Click Origin criterion image
            origin_criterion_img = self.wait_and_find_element(
                By.CSS_SELECTOR, 
                "img[alt='Origin criterion'][title='Origin criterion']"
            )
            origin_criterion_img.click()
            time.sleep(3)  # Increased wait time for popup to appear
            
            # Switch to the iframe
            iframe = self.wait_and_find_element(
                By.NAME,
                "PopupSelectOriginCriteria"
            )
            self.driver.switch_to.frame(iframe)
            time.sleep(2)  # Added wait after switching to iframe
            
            # Wait for the grid to be visible
            WebDriverWait(self.driver, 15).until(  # Increased timeout
                EC.visibility_of_element_located((By.ID, "ctl00_cplhContainer_radGridOriginCriteria"))
            )
            time.sleep(2)  # Added wait after grid is visible
            
            # Get origin criteria from goods data
            origin_criteria = row.get('Origin Criteria', '').upper()
            
            # Uncheck all checkboxes first
            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
            for cb in checkboxes:
                if cb.is_selected():
                    cb.click()
                    time.sleep(1)  # Increased wait between unchecking
            
            # Select the appropriate checkbox based on origin criteria
            if origin_criteria in ['CC', 'CTH', 'CTSH', 'CTC', 'PSR', 'WO']:
                checkbox_id = f"chk{origin_criteria}"
                try:
                    checkbox = WebDriverWait(self.driver, 15).until(
                        EC.element_to_be_clickable((By.ID, checkbox_id))
                    )
                    if not checkbox.is_selected():
                        checkbox.click()
                        time.sleep(1)  # Increased wait after checking
                except Exception as e:
                    print(f"Warning: Could not select criteria {origin_criteria}: {str(e)}")
            
            # Click the Select button
            select_button = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "a.rtbWrap[accesskey='S']"))
            )
            select_button.click()
            time.sleep(1)  # Increased wait for window to close
            
            # Switch back to default content
            self.driver.switch_to.default_content()
            time.sleep(1)  # Added wait after switching back
            
            # Click Add Item button
            add_button = self.wait_and_find_element(By.ID, "ctl00_cplhContainer_btnAddItem")
            add_button.click()
            time.sleep(5)  # Increased wait to 5 seconds after adding item
        
        # After all goods are added, click the save button using the image
        try:
            save_image = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "img[src='/Images/saveitem.gif']"))
            )
            save_image.click()
            time.sleep(2)  # Wait for save operation to complete
        except Exception as e:
            print(f"Warning: Could not click save button: {str(e)}")
            raise 